from model_utils import train_model, predict_sms

if __name__ == "__main__":
    # Train the model
    train_model()

    # Test the model
    test_sms = [
        "Your electricity bill of $150 is due on Feb 10.",
        "Congratulations! You won a free gift card!",
    ]
    predictions = predict_sms(test_sms)
    for sms, pred in zip(test_sms, predictions):
        print(f"SMS: {sms}\nPrediction: {pred}\n")
