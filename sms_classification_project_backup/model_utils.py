import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report
from transformers import DistilBertTokenizer, DistilBertForSequenceClassification, Trainer, TrainingArguments
from torch.utils.data import Dataset

class SMSDataset(Dataset):
    def __init__(self, texts, labels, tokenizer, max_len):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_len = max_len

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        label = self.labels[idx] if self.labels is not None else -1
        encoding = self.tokenizer(
            text, max_length=self.max_len, padding="max_length", truncation=True, return_tensors="pt"
        )
        return {
            "input_ids": encoding["input_ids"].squeeze(0),
            "attention_mask": encoding["attention_mask"].squeeze(0),
            "labels": label,
        }

def load_data(file_path, tokenizer, max_len=128):
    data = pd.read_csv(file_path)
    texts = data["sms_text"].tolist()
    labels = data.get("label", None)
    label_map = {"bill_related": 0, "not_bill_related": 1}
    if labels is not None:
        labels = [label_map[label] for label in labels]
    return SMSDataset(texts, labels, tokenizer, max_len)

def train_model():
    model_path = "/Users/<USER>/Work/ai/distilbert_model"
    tokenizer = DistilBertTokenizer.from_pretrained(model_path)
    model = DistilBertForSequenceClassification.from_pretrained(model_path, num_labels=2)

    train_dataset = load_data("data/train_data.csv", tokenizer)
    eval_dataset = load_data("data/test_data.csv", tokenizer) 
    training_args = TrainingArguments(
        output_dir="./results",
        num_train_epochs=3,
        per_device_train_batch_size=16,
        evaluation_strategy="epoch",
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset  # Add eval dataset here
    )
    trainer.train()
    model.save_pretrained("./model")
    tokenizer.save_pretrained("./model")

def predict_sms(sms_texts):
    tokenizer = DistilBertTokenizer.from_pretrained("./model")
    model = DistilBertForSequenceClassification.from_pretrained("./model")
    inputs = tokenizer(sms_texts, return_tensors="pt", padding=True, truncation=True)
    outputs = model(**inputs)
    predictions = outputs.logits.argmax(dim=1)
    return ["bill_related" if pred == 0 else "not_bill_related" for pred in predictions]
