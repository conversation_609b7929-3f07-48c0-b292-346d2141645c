import unittest
from model_utils import predict_sms

class TestSMSClassification(unittest.TestCase):
    def test_bill_related_sms(self):
        sms = ["Your water bill of $100 is due by Feb 1"]
        prediction = predict_sms(sms)
        self.assertEqual(prediction[0], "bill_related")

    def test_not_bill_related_sms(self):
        sms = ["Congrats! You won a free trip!"]
        prediction = predict_sms(sms)
        self.assertEqual(prediction[0], "not_bill_related")

if __name__ == "__main__":
    unittest.main()
