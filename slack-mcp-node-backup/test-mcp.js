const axios = require('axios');

// Test the MCP server
async function testMCP() {
  try {
    console.log('🔍 Testing MCP server health...');
    const healthResult = await axios.get('http://localhost:3000/api/mcp/health');
    console.log('✅ Health check successful!');
    console.log(JSON.stringify(healthResult.data, null, 2));
    
    console.log('\n🔍 Testing MCP context endpoint...');
    console.log('Fetching messages from the past 200 days...');
    
    // Calculate dates for the past 200 days
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 200);
    
    const contextResult = await axios.get('http://localhost:3000/api/mcp/context', {
      params: {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        max_messages: 1000 // Increase the limit
      }
    });
    
    if (contextResult.data.status === 'success' && contextResult.data.context) {
      const context = contextResult.data.context;
      console.log(`✅ Successfully retrieved ${context.metadata.message_count} messages!`);
      console.log(`📺 Channel: ${context.channel.name} (${context.channel.id})`);
      console.log(`📅 Time range: ${context.metadata.time_range.earliest} to ${context.metadata.time_range.latest}`);
      
      if (context.messages.length > 0) {
        console.log(`\n📝 All messages from the last 200 days:`);
        context.messages.forEach((msg, idx) => {
          console.log(`\n[${idx + 1}]`);
          console.log(JSON.stringify(msg, null, 2));
        });
      } else {
        console.log('\n⚠️ No messages found in this time range.');
      }
    } else {
      console.log('❌ Error retrieving context:');
      console.log(JSON.stringify(contextResult.data, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Error testing MCP server:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n⚠️ Server not running. Start it with: npm start');
    } else if (error.response) {
      console.log('\n⚠️ Server returned an error:');
      console.log(JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testMCP().catch(console.error); 