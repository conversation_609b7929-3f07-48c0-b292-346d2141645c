require('dotenv').config();
const { WebClient } = require('@slack/web-api');

const slack = new WebClient(process.env.SLACK_BOT_TOKEN);

(async () => {
  try {
    const result = await slack.conversations.list({ types: 'private_channel' });
    result.channels.forEach(c => {
      console.log(`${c.name}: ${c.id} (is_member: ${c.is_member})`);
    });
  } catch (error) {
    console.error('Error listing private channels:', error);
  }
})(); 