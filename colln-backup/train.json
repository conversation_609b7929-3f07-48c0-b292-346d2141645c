[{"text": "We have received a payment of INR 3577 for your DMI loan ID DMI0036204986 against your loan EMI Payment. To know more login on portal.dmifinance.in", "label": "Bill paid", "entities": [{"start": 60, "end": 73, "label": "account_number"}]}, {"text": "Dear <PERSON><PERSON><PERSON><PERSON> , EMI of Rs 6532.0 against your loan a/c number DMI0036557548 is due on 1/5/2025 . Please maintain a sufficient balance in the account registered with us. DMI Finance", "label": "<PERSON>", "entities": [{"start": 32, "end": 38, "label": "amount"}, {"start": 68, "end": 81, "label": "account_number"}]}, {"text": "Dear Customer, EMI of Rs. 1265 against your loan ID DMI0048054030 is due on 05-01-2025. Please maintain a sufficient balance in your a/c registered with us by 04-01-2025 to avoid late payment fees. Kindly ignore if already paid. DMI Finance.", "label": "BIll Gen", "entities": [{"start": 52, "end": 65, "label": "account_number"}]}, {"text": "Your Loan no DMIXXXXXX4425 is overdue. Sigma Outsourcing Pvt Ltd our authorized partner based at New Delhi will contact you for collecting the overdue amount. Click on https://rzp.io/DMIPCC/lV3uI42f to make immediate payment. Kindly ignore if already paid. DMI Finance.", "label": "Overdue", "entities": []}, {"text": "Hello Shanid, DMI Finance has requested payment of INR 2369 against your LOAN DMI0038161422 You can pay using the following link : https://rzp.io/DMIPCC/1q8IiQBO", "label": "<PERSON>", "entities": [{"start": 78, "end": 91, "label": "account_number"}]}, {"text": "Dear Customer, Payment against your loan id DMI0045123398 is overdue. Make immediate payment by clicking on the http://1kx.in/DMISMS/arNENF . Kindly ignore if already paid. DMI Finance.", "label": "Overdue", "entities": [{"start": 44, "end": 57, "label": "account_number"}]}, {"text": "We have received a payment of INR 5461.60 for your DMI loan ID DMI0045962444 on 01/01/2025 . Your loan account will be updated within 72 hours. DMI Finance", "label": "<PERSON>", "entities": [{"start": 63, "end": 76, "label": "account_number"}]}, {"text": "Received a payment of Rs 649 for your loan DMI0047040560. It will be adjusted against upcoming EMI/charges as per due date.To know more click portal.dmifinance.in", "label": "<PERSON>", "entities": [{"start": 43, "end": 56, "label": "account_number"}]}, {"text": "Loan: MGM-100, Due Amount Rs.14539/-. To pay now: https://muthoot.biz/MUTHUT/8vOGb7O7 . Other payment options: https://muthoot.biz/MUTHUT/WMd9w0dk - Muthoot Finance", "label": "BIll Gen", "entities": [{"start": 6, "end": 13, "label": "account_number"}]}, {"text": "Dear Customer, payment of Rs. 1379 is received for your Business Loan MFLUNBRORIL000005408689 on 02-JAN-25. Muthoot Fincorp Limited.", "label": "<PERSON>", "entities": [{"start": 70, "end": 93, "label": "account_number"}]}, {"text": "Dear <PERSON><PERSON><PERSON> Customer,Interest of Rs.180151/- is due on your Gold loan Account No.2076MDL4512. If you can't pay total due amount now, please PAY Rs.3293/- MINIMUM DUE AMOUNT (MDC) . EARN 24Ct. GOLD UNDER MILLIGRAM REWARD PROGRAM. Visit your Branch for clarification & ignore if paid. T&C apply.", "label": "BIll Gen", "entities": [{"start": 82, "end": 93, "label": "account_number"}]}, {"text": "Dear sir,Rs.51418.00 credited to Loan account MDL-4120 as repayment of loan at Muthoot Finance on 02/01/2025 16:05 .", "label": "Identifier", "entities": [{"start": 12, "end": 19, "label": "amount"}, {"start": 46, "end": 54, "label": "account_number"}]}, {"text": "प्रिय ग्राहक, गोल्ड लोन क्रमांक व रुपए  ********** for Rs 135721 क्रमशः हमारी शाखा  VIKAS NAGAR dt 01/01/2025 से दर्ज की गई दिनांक के दिन प्राप्त करने के लिए धन्यवाद। इस लोन के लिए हमारा सह-उधार साझेदार बैंक DCB है। ऋण और सह-उधार व्यवस्था के नियम और शर्तें गिरवी दस्तावेजों में उल्लिखित हैं। सह-उधार के नियम और शर्तें हमारी वेबसाइट https://www.muthootfincorp.com/super-value-gold-loan/ पर भी उपलब्ध हैं। मुथूट फिनकॉर्प लिमिटेड।", "label": "Identifier", "entities": [{"start": 40, "end": 50, "label": "account_number"}]}, {"text": "EMI Rs. 15957 on your TVS Credit Loan a/c *************** is due on 5th Jan 25. Maintain sufficient bank a/c balance 3 days before due date to avoid bounce charges. You can also pay EMI in advance to our executive or to pay on our portal, visit: m2sm.com/CDGyQKqGI  . For other online modes, visit: m2sm.com/CDGzITYn6  . Ignore, if already paid.", "label": "BIll Gen", "entities": [{"start": 42, "end": 57, "label": "account_number"}]}, {"text": "Dear <PERSON><PERSON>, Your EMI payment of Rs. 1750 for the TVS Credit loan a/c no.-*************** is due on 03-01-2025. Since your ACH is not registered, please make the payment online. Please ignore if already paid. Click here to pay m2sm.com/DbQAHpF1x ", "label": "BIll Gen", "entities": [{"start": 80, "end": 95, "label": "account_number"}]}, {"text": "EMI Rs.5143 on your TVS Credit Loan no.*************** is due on 03-01-2025. If 03-01-2025 is a holiday, EMI will be presented on the next working day. Maintain sufficient balance in bank a/c to avoid levy of penal charges at 36% per annum on unpaid installment & CIBIL impact. Once debited, EMI status will reflect in our system in 3 working days. Beware of fraud calls/messages asking you to pay EMIs to personal accounts. To know more, click https://tvscs.in/TVSCS/FoZ46F8 .", "label": "BIll Gen", "entities": [{"start": 39, "end": 54, "label": "account_number"}]}, {"text": "Your online payment of Rs. 1485 with ref. no. 173580215162684400 for your TVS Credit Loan a/c *************** has been received & updated on 02/01/2025.", "label": "<PERSON>", "entities": [{"start": 94, "end": 109, "label": "account_number"}]}, {"text": "Dear Customer, Thank you for the cash payment of Rs.2919.0 made on 2024-12-31 20:50 towards your loan a/c no- ***************. It would take up to 2 working days to update the payment in your loan a/c. Download the receipt(************) by clicking here- https://tvscs.in/TVSCS/0kDYUfm. Beware of fraudulent calls/messages asking you to make payments to a personal bank/UPI account or via unknown web links. Stay alert and stay safe. TVS Credit", "label": "<PERSON>", "entities": [{"start": 52, "end": 58, "label": "amount"}, {"start": 110, "end": 125, "label": "account_number"}]}, {"text": "Hi, EMI for your HDB Loan a/c ******** is due on 4th Jan25. Fund ur a/c before EMI date to avoid charges. Payment cleared will be updated in 3 Working Days", "label": "<PERSON>", "entities": [{"start": 30, "end": 38, "label": "account_number"}]}, {"text": "ONE CLICK is all it takes. Pay your o/s of Rs 5145 towards HDB loan no. 9106261 on UPI. Visit 1kx.in/A7IeAGv1zZ0. Ignore if already paid.", "label": "<PERSON>", "entities": [{"start": 72, "end": 79, "label": "account_number"}]}, {"text": "Dear Cus, there are charges of Rs. 708 o/s in your HDB Loan a/c ******** .Pls pay online at https://www.hdbfs.com/customer-services/make-payment Pls ignore if paid.", "label": "<PERSON>", "entities": [{"start": 64, "end": 72, "label": "account_number"}]}, {"text": "Dear customer,Total unpaid penal charges as of 30-NOV-2024 in your loan account ******** stands at Rs. 151 on account of your unpaid/delayed installments. You may pay online www.hdbfs.com along with installments due if any and get your a/c regularized with us. Please ignore if already paid - HDBFS", "label": "<PERSON>", "entities": [{"start": 80, "end": 88, "label": "account_number"}]}, {"text": "Dear Customer, EMI Payment of Rs. 2417 against Loan no ******** for the month of 04-DEC-2024 is received and updated in our records. Thank You - HDBFS", "label": "<PERSON>", "entities": [{"start": 55, "end": 63, "label": "account_number"}]}, {"text": "Dear customer, Please make the payment for your Home Credit loan at https://gi9.in/HOMECR/yn7pYb .", "label": "<PERSON>", "entities": []}, {"text": "Dear Customer, pay your Home Credit loan dues using any of our secure digital payment methods. Pay via mobile app https://gi9.in/HOMECR/T6y2uc Pay on our website https://gi9.in/HOMECR/H9EKet Pay via Paytm https://gi9.in/HOMECR/e5n6Po", "label": "<PERSON>", "entities": []}, {"text": "Dear Customer, Thank you for the Payment of Rs.1788.0, One Thousand Seven Hundred Eighty Eight Rupees Zero Paise for your Home Credit  Loan No.-**********, Transaction id *****************. Your account will be updated within 72 hours.  Click Here For Receipt - https://apb.dev/AIRBNK/6aFASnZr ", "label": "<PERSON>", "entities": [{"start": 47, "end": 53, "label": "amount"}, {"start": 144, "end": 154, "label": "account_number"}]}, {"text": "ATTENTION! You have not paid Rs. 2603 for Home Credit Loan ********** This will impact your CIBIL score. Pay TODAY at hcin.in/AWT2CI3Y", "label": "<PERSON>", "entities": [{"start": 59, "end": 69, "label": "account_number"}]}, {"text": "CRITICAL! Your Home Credit Loan ********** is overdue by Rs 10974. For closure of your Loan pay immediately via http://pr0.io/HOMECR/4C87CD", "label": "Overdue", "entities": [{"start": 32, "end": 42, "label": "account_number"}]}, {"text": "Dear customer, payment towards your Home Credit Loan number ********** is overdue. ELEVATE DIRECT on behalf of Home Credit may visit you regarding the same. For any query call 0124-6259980. Ignore if already paid.", "label": "<PERSON>", "entities": [{"start": 60, "end": 70, "label": "account_number"}]}, {"text": "Overdue of Rs.12,692 for Personal loan with HERO FINCORP LTD via Paytm. Principal: Rs.9,555 Interest:Rs. 2,937 Penal Charges: Rs.0 (Overdue Penal Charge) + Rs.200 (Bounce Charges) = Rs.200 Pay now @ https://paytm.me/CMSMEP/4HDVxSP to avoid credit score impact. Ignore if paid-Paytm", "label": "Overdue", "entities": []}, {"text": "Your EMI of Rs. 4005 on Hero Fincorp loan a/c no. HOO0PL00100012913507 is due soon. Please ensure adequate balance in your account. View secure payment options connect.herofincorp.com/zE4m0qX", "label": "<PERSON>", "entities": [{"start": 50, "end": 70, "label": "account_number"}]}, {"text": "Dear customer, EMI for your Hero Fincorp loan ending with 1044 is due soon. Please ensure to keep adequate balance in your account to avoid late payment charges. To know more about us, please click on the link https://www.herofincorp.com/ , Thank You - Hero Fincorp", "label": "<PERSON>", "entities": []}, {"text": "\"Dear Customer,\nYour EMI of 3370 for your Personal Loan is due on 2025-01-08. Make timely payments to avoid late fees and maintain financial wellness.\nClick here to pay: https://crdg.in/HEROFI/iSWRx1\nWishing you and your family a Happy New Year! \nHero FinCorp\"", "label": "<PERSON>", "entities": [{"start": 66, "end": 76, "label": "due_date"}]}, {"text": "Dear customer, by paying on time last month you avoided Rs. 350 in bounce charges and late fees. Keep up the timely payments on Loan Account No UGETWL00100016896414 to continue avoiding these charges. connect.herofincorp.com/KkV86Mr", "label": "Identifier", "entities": [{"start": 144, "end": 164, "label": "account_number"}]}, {"text": "Dear Customer,Your EMI of Rs. 12957 towards your Auto loan is due on the 8th. Timely payments help to build your Credit Score. Kindly maintain a sufficient balance in your account before the due date. Click here to pay https://crdg.in/HEROFI/7jagWh - Hero Fincorp", "label": "<PERSON>", "entities": []}, {"text": "Dear Customer, Your loan account T04800270323112919 is due with unpaid charges. Kindly click here https://ltfinance.in/LNTFIN/xwJeXVa4 to pay your overdue charges to avoid any adverse impact on credit ratings. Ignore if paid. Regards, L&T Finance.", "label": "<PERSON>", "entities": [{"start": 33, "end": 51, "label": "account_number"}]}, {"text": "Payment of Rs. 4862.0 for your L&T Finance loan T06894170824032224 is overdue. Click pmny.in/PAYUMN/SrvqOkwthHvW to pay online now and avoid adverse credit rating or additional charges. Pls ignore if already paid. Team PayU", "label": "Overdue", "entities": [{"start": 15, "end": 21, "label": "amount"}, {"start": 48, "end": 66, "label": "account_number"}]}, {"text": "Dear Customer, EMI debit of Rs.5218/- against your loan account T04260110424043739 will be processed on 03-JAN-2025. Ensure sufficient balance in your bank account XX6197 to avoid penal charges of 2% per month on overdue EMI and bounce charges as per schedule. Ignore if paid. L&T Finance", "label": "<PERSON>", "entities": [{"start": 64, "end": 82, "label": "account_number"}]}, {"text": "Dear Customer, Thank you for payment of Rs.6329/- towards the loan account number T05188030424061006. Regards, L&T Finance Ltd.", "label": "<PERSON>", "entities": [{"start": 82, "end": 100, "label": "account_number"}]}, {"text": "Dear Customer, Thank you for payment of Rs.4388/- towards the loan account number T04116300922062927. Regards, L&T Finance Ltd.", "label": "<PERSON>", "entities": [{"start": 82, "end": 100, "label": "account_number"}]}, {"text": "Dear Customer,Payment of Rs. 2105.00 made towards loan A/c ending 0880 is credited to the A/c on 02/01/2025. Call @ *********** for queries.Ujjivan SFB", "label": "<PERSON>", "entities": [{"start": 29, "end": 35, "label": "amount"}, {"start": 67, "end": 70, "label": "account_number"}]}, {"text": "Dear <PERSON><PERSON>, Rs. 2621.00 is overdue in your Ujjivan SFB Loan A/c ending with 1818 as on 18/12/2024. Please pay immediately, if already not paid. Reach out to the nearest branch for queries.", "label": "Overdue", "entities": [{"start": 18, "end": 24, "label": "amount"}, {"start": 78, "end": 82, "label": "account_number"}]}, {"text": "Dear customer,We wish to inform you that there has been a revision to your loan schedule linked to Loan A/c ***************. Now you can get the revised schedule from Hello Ujjivan App or the nearest branch. For any query please call *********** or our Relationship Officer", "label": "Identifier", "entities": [{"start": 108, "end": 123, "label": "account_number"}]}, {"text": "Dear <PERSON><PERSON><PERSON>, Received payment of Rs. 1562.00 in your Ujjivan SFB Loan A/c ending with 9445 on 14/12/2024. Credit will be given subject to T&C. Reach out to the nearest branch for queries.", "label": "<PERSON>", "entities": [{"start": 50, "end": 56, "label": "amount"}, {"start": 99, "end": 103, "label": "account_number"}]}, {"text": "Dear Customer, your loan application no ************ with Ujjivan SFB is approved and it will be disbursed shortly. For any query please call 1800-208- 2121.", "label": "Identifier", "entities": []}, {"text": "REMINDER of upcoming IIFL Loan EMI payment of Rs.6905 due on 05-Jan-2025. Maintain sufficient balance in your bank account.Ignore if paid. - IIFLHL", "label": "<PERSON>", "entities": []}, {"text": "Dear Customer, We have received payment of INR 1091.00 against your Loan No. GL35649377 on 02/01/2025 03:42:44 PM. It has been credited to loan account.-IIFL - IIFLFL", "label": "<PERSON>", "entities": [{"start": 47, "end": 53, "label": "amount"}, {"start": 77, "end": 87, "label": "account_number"}]}, {"text": "IIFL Loan A/C  Pay outstanding INR 8714.0 today. Delay will impact your credit score. To Pay https://iifl.io/IIFLFN/qpay Ignore if paid. - IIFLHL", "label": "Overdue", "entities": [{"start": 35, "end": 41, "label": "amount"}]}, {"text": "Dear Customer, please pay due interest Rs 2820.48 on your Gold Loan GL35755820 by ******** to avail max applicable rebate. Visit https://iifl.io/IIFLFN/qpay to pay - IIFLFL", "label": "<PERSON>", "entities": [{"start": 42, "end": 49, "label": "amount"}, {"start": 68, "end": 78, "label": "account_number"}]}, {"text": "Dear Customer, your refund for IIFL Loan Account SL4160743-1789.58 is to be processed. Please update your bank account details in the link provided https://iifl.io/IIFLFN/O0IzuTh , else email <NAME_EMAIL>. Post validation, the refund will be processed in 7 working days. - IIFLFL", "label": "Identifier", "entities": [{"start": 49, "end": 58, "label": "account_number"}]}, {"text": "Loan no GL36031113 for INR 41367.00 is disbursed on 20/12/2024. First payment due on 20/09/2025. To get borrower copy download IIFL loans app from https://iifl.io/IIFLFN/ilapp or login to https://apps.iifl.com - IIFLFL  -  IIFLFL", "label": "<PERSON>", "entities": [{"start": 8, "end": 18, "label": "account_number"}]}, {"text": "We have received the Amount Rs. 2520.00 against your Loan Account No ************. Belstar", "label": "<PERSON>", "entities": [{"start": 69, "end": 81, "label": "account_number"}]}, {"text": "Dear Customer, Amount of Rs. 9030.00 on your loan BLUN000002041424 is due on 04-Jan-2025. Kindly make the payment to avoid any additional charges. Please ignore if already paid- Belstar.", "label": "<PERSON>", "entities": [{"start": 29, "end": 35, "label": "amount"}, {"start": 50, "end": 66, "label": "account_number"}]}, {"text": "Your Loan A/C246400100348Amount Rs.60000.00was disbursed. Belstar", "label": "Others", "entities": []}, {"text": "Reminder-Kindly pay due for Rs.3137.00 FOR 20 Apr 2024 ON LOAN NO.5029051, as promised by you.\nPls ignore if already paid -Belstar", "label": "Overdue", "entities": [{"start": 31, "end": 37, "label": "amount"}, {"start": 66, "end": 73, "label": "account_number"}]}, {"text": "ECS Alert! Overdue amt of Rs. 500.00 for LAN V51DDMLR587848 is bounced on 31-Dec-2024 due to INSUFFICIENT FUNDS Bajaj Finance Ltd", "label": "Others", "entities": [{"start": 30, "end": 35, "label": "amount"}, {"start": 45, "end": 59, "label": "account_number"}]}, {"text": "Alert! Amount of Rs. 6,904 is overdue for LAN 4010CDLB652195 towards monthly installment of Rs. 1,999; Penal charges of Rs. 179; Bounce Charges of Rs. 4,500; Other Charges of Rs. 226. Click to pay https://r.bflcomm.in/BAJAJF/K5DlhiC8 Ignore if paid. Bajaj Finance Ltd", "label": "Overdue", "entities": [{"start": 46, "end": 60, "label": "account_number"}]}, {"text": "Welcome Letter! Your new loan a/c no. 4A1DPFMK929933 is booked for loan amt 74900, 1st EMI Due Date 02-FEB-2025. View loan details https://m.bflcomm.in/BAJAJF/KgPGH4 . Share your feedback https://m.bflcomm.in/BAJAJF/KgPGHM . Bajaj Finance Ltd", "label": "<PERSON>", "entities": [{"start": 38, "end": 52, "label": "account_number"}]}, {"text": "Quick note! Rs. 3428.0 due for LAN 89Q0CDLH347579, pay now to prevent CIBIL impact https://m.bflcomm.in/BAJAJF/KDrCze *Ignore if paid. Bajaj Finance Ltd", "label": "<PERSON>", "entities": [{"start": 16, "end": 22, "label": "amount"}, {"start": 35, "end": 49, "label": "account_number"}]}, {"text": "Card Blocked! Your EMI Network Card ending 1518 is permanently blocked due to loan payment inconsistency. Click https://m.bflcomm.in/BAJAJF/01kIET for details. Bajaj Finance Ltd", "label": "Others", "entities": [{"start": 43, "end": 47, "label": "account_number"}]}, {"text": "Alert! Your EMI of Rs. 5,833 is bounced for Loan No.572DPFLP429580.Click https://r.bflcomm.in/BAJAJF/InlaZ05M to pay your total overdue. Bajaj Finance Ltd", "label": "Overdue", "entities": [{"start": 52, "end": 66, "label": "account_number"}]}, {"text": "Attention! Loan application no. ******** is on hold due to a discrepancy in documents. Please connect with the customer. Bajaj Finance Ltd", "label": "others", "entities": []}, {"text": "Update! Your overdue payment of Rs. 3076.00 for loan A/c no. P16JPRP8140357 will be processed in 1 business day & the same will be updated in your Statement of Account, visit the 'View Statement' section. Bajaj Finance Ltd", "label": "others", "entities": [{"start": 36, "end": 42, "label": "amount"}, {"start": 61, "end": 75, "label": "account_number"}]}, {"text": "Alert! Request to pay overdue amt on your Loan a/c L2WRAI10773778. Pay through My BFL app or Click https://go.bflaf.com/BAJAJF/77de1c17b to pay now. Bajaj Finance", "label": "Overdue", "entities": [{"start": 51, "end": 65, "label": "account_number"}]}, {"text": "Dear Sir/Madam, An amount of Rs. 4723/-  for your Tata Capital Personal Loan loan no. ending with 9007 is due on 05-01-2025. You can make pre-payment of the dues by clicking here https://gs.im/Tatacl/e/O9fuVvgGjaw . Dues will not be debited from your bank a/c if payment is made on or before 03-01-2025. Kindly ignore if already paid - Tata Capital", "label": "BIll Gen", "entities": [{"start": 98, "end": 102, "label": "account_number"}]}, {"text": "Improve your CIBIL Score with us.\n\nDear Customer, \nYour EMI of Rs 45100.000000 for TATA CAPITAL HOUSING FINANCE LIMITED Loan ending with 3819 is due on 05-01-2025. We encourage our customers to make payments within the due date to have a good repayment track record. Please note paying EMI on time helps you maintain your credit score. Please ensure to keep an adequate balance in your account. Kindly confirm the availability of sufficient balance in your bank account by clicking on tcsms.io/Tatacl/e/A7HA6OGvafj Thank you.", "label": "BIll Gen", "entities": [{"start": 66, "end": 73, "label": "amount"}, {"start": 137, "end": 141, "label": "account_number"}]}, {"text": "Dear SARDHA MORLE, we have received Rs. 2140.00 for Tata Capital loan acc no. ending with XXXXXXXX1930  from you against the instalment demand of Rs. 2140.00. Click https://gs.im/Tatacl/e/G45IbedItCu to download e-receipt. Thank you  - Tata Capital", "label": "<PERSON>", "entities": [{"start": 40, "end": 46, "label": "amount"}, {"start": 98, "end": 102, "label": "account_number"}]}, {"text": "Dear MANTOR DEVI, EMI of Rs.7520.00 for your Tata Capital Loan Acc No. ending with XXXXXXXX0545 is due on 03 Jan 2025. Kindly ensure you have adequate amount in hand or balance in your Bank Account one day prior.Tata Capital", "label": "BIll Gen", "entities": [{"start": 28, "end": 34, "label": "amount"}, {"start": 92, "end": 95, "label": "account_number"}]}, {"text": "Dear Sir/<PERSON><PERSON>, Thank you for your confirmation. EMI of Rs.3793 for your Tata Capital two_wheeler loan account no ending with 9344 is due on 5-JAN-25. Kindly maintain sufficient balance in your bank a/c one day prior to avoid paying charges later. If your NACH Mandate is registered with us, then EMI will be deducted even if it's a holiday on EMI date.", "label": "BIll Gen", "entities": [{"start": 126, "end": 130, "label": "account_number"}]}, {"text": "Dear Customer, payment of ₹2650.00 for your Annapurna loan a/c no. 20920593is received on Jan  1 2025  8:12PM. TXN No: ********************. For any queries call at *********** Team Annapurna", "label": "<PERSON>", "entities": [{"start": 27, "end": 33, "label": "amount"}, {"start": 67, "end": 75, "label": "account_number"}]}, {"text": "Payment of Rs.4152 for  Annapurna Finance Private Limited-MFI Loan Repayment bill (********) via PhonePe  (Txn Id: *************************) has failed. If deducted, it will be refunded within 7 - 9 working days.", "label": "<PERSON>", "entities": [{"start": 83, "end": 91, "label": "account_number"}]}, {"text": "Rs.2651.00 debited A/cXX1832 and credited to Annapurna Finance - MFI Loan via UPI Ref No ************ on 02Jan25. Call ***********, if not done by you. -BOI", "label": "<PERSON>", "entities": [{"start": 3, "end": 9, "label": "amount"}]}, {"text": "Sent Rs.2400.00\nFrom HDFC Bank A/C x7837\nTo Annapurna Finance - MFI Loan EMI\nOn 02/01/25\nRef ************\nNot You?\nCall ***********/SMS BLOCK UPI to **********", "label": "<PERSON>", "entities": [{"start": 8, "end": 14, "label": "amount"}]}]