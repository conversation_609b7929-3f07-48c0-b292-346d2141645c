import os
import torch
from transformers import AutoTokenizer, AutoModelForSequenceClassification, AutoModelForTokenClassification
from datasets import Dataset
from ner_utils import fine_tune_ner_model, predict_ner, pretrain_on_conll
import json

# Paths
MODEL_PATH = "./models/distilbert_model/"
TRAINED_MODEL_PATH = "./models/trained_model/"
LABEL_MAP = {
    "BILL": 1,
    "NON_BILL": 0,
    "Bill paid": 1,
    "Bill Paid": 1,
    "Bill Gen": 1,
    "BIll Gen": 1,
    "Overdue": 1,
    "Identifier": 1
}
REVERSE_LABEL_MAP = {v: k for k, v in LABEL_MAP.items()}

# Load tokenizer and model
tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
model = AutoModelForSequenceClassification.from_pretrained(MODEL_PATH, num_labels=len(LABEL_MAP))

# Preprocess data for classification
def preprocess_data(texts, labels=None):
    encodings = tokenizer(texts, padding=True, truncation=True, max_length=128, return_tensors="pt")
    if labels is not None:
        encodings["labels"] = torch.tensor([LABEL_MAP[label] for label in labels])
    return encodings

# Train the classification model
def train_model(train_file, val_file):
    from transformers import Trainer, TrainingArguments

    # Load datasets
    with open(train_file, "r") as f:
        train_data = json.load(f)
    with open(val_file, "r") as f:
        val_data = json.load(f)

    train_texts = [item["text"] for item in train_data]
    train_labels = [item["label"] for item in train_data]

    val_texts = [item["text"] for item in val_data]
    val_labels = [item["label"] for item in val_data]

    # Preprocess data
    train_encodings = preprocess_data(train_texts, train_labels)
    val_encodings = preprocess_data(val_texts, val_labels)

    class SMSDataset(torch.utils.data.Dataset):
        def __init__(self, encodings):
            self.encodings = encodings

        def __len__(self):
            return len(self.encodings["input_ids"])

        def __getitem__(self, idx):
            return {key: val[idx] for key, val in self.encodings.items()}

    train_dataset = SMSDataset(train_encodings)
    val_dataset = SMSDataset(val_encodings)

    training_args = TrainingArguments(
        output_dir="./results",
        evaluation_strategy="epoch",
        logging_dir="./logs",
        logging_steps=10,
        per_device_train_batch_size=8,
        per_device_eval_batch_size=8,
        num_train_epochs=3,
        save_strategy="epoch",
        load_best_model_at_end=True,
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=val_dataset,
        tokenizer=tokenizer,
    )

    trainer.train()
    trainer.save_model(TRAINED_MODEL_PATH)
    tokenizer.save_pretrained(TRAINED_MODEL_PATH)
    print(f"Model fine-tuned and saved to {TRAINED_MODEL_PATH}.")

# Predict function for classification and entity extraction
def predict(text):
    # Load models
    tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
    classifier = AutoModelForSequenceClassification.from_pretrained(MODEL_PATH)
    
    # For NER, load from the trained NER model path
    try:
        ner_tokenizer = AutoTokenizer.from_pretrained("./models/trained_ner_model/")
        ner_model = AutoModelForTokenClassification.from_pretrained(
            "./models/trained_ner_model/",
            num_labels=7,  # Number of labels in LABEL_LIST from ner_utils.py
            id2label={i: label for i, label in enumerate([
                "O",
                "B-amount", "I-amount",
                "B-account_number", "I-account_number",
                "B-due_date", "I-due_date"
            ])},
            label2id={label: i for i, label in enumerate([
                "O",
                "B-amount", "I-amount",
                "B-account_number", "I-account_number",
                "B-due_date", "I-due_date"
            ])}
        )
    except Exception as e:
        print(f"Warning: NER model not found or error loading it: {str(e)}")
        print("Please train the NER model first using option 2.")
        return {"label": "UNKNOWN", "entities": []}
    
    # Classification
    inputs = tokenizer(text, return_tensors="pt", truncation=True, padding=True)
    outputs = classifier(**inputs)
    prediction = torch.argmax(outputs.logits).item()
    
    # NER
    entities = predict_ner(text, ner_model, ner_tokenizer)
    
    return {
        "label": REVERSE_LABEL_MAP[prediction],
        "entities": entities
    }

def main():
    print("Choose an option:")
    print("1. Train the classification model")
    print("2. Train the NER model for entity extraction")
    print("3. Classify and extract entities from an SMS message")
    print("4. Pre-train on CoNLL-2003 and then fine-tune NER model")
    choice = input("Enter your choice: ")

    if choice == "1":
        train_model("./data/train.json", "./data/test.json")
    elif choice == "2":
        fine_tune_ner_model("./data/train.json")
    elif choice == "3":
        text = input("Enter the SMS message: ")
        result = predict(text)
        print(f"Prediction: {result}")
    elif choice == "4":
        conll_path = "/Users/<USER>/Work/ai/dataset/conll2003.zip"
        # First pre-train on CoNLL
        pretrained_path = pretrain_on_conll(conll_path)
        # Then fine-tune on our data
        fine_tune_ner_model("./data/train.json", pretrained_path)
    else:
        print("Invalid choice.")

if __name__ == "__main__":
    main()

