from datasets import Dataset, load_dataset
from transformers import AutoTokenizer, AutoModelForTokenClassification, Trainer, TrainingArguments
import json
import torch
import zipfile
import os

NER_MODEL_PATH = "./models/trained_ner_model/"
MODEL_PATH = "./models/distilbert_model/"

# Our final label list
LABEL_LIST = [
    "O",
    "B-amount", "I-amount",
    "B-account_number", "I-account_number",
    "B-due_date", "I-due_date"
]

# CoNLL-2003 labels
CONLL_LABELS = [
    "O",
    "B-PER", "I-PER",
    "B-ORG", "I-ORG",
    "B-LOC", "I-LOC",
    "B-MISC", "I-MISC"
]

label2id = {label: i for i, label in enumerate(LABEL_LIST)}
id2label = {i: label for i, label in enumerate(LABEL_LIST)}

conll_label2id = {label: i for i, label in enumerate(CONLL_LABELS)}
conll_id2label = {i: label for i, label in enumerate(CONLL_LABELS)}

def load_conll_data(zip_path):
    # Extract the zip file
    extract_dir = os.path.dirname(zip_path)
    with zipfile.ZipFile(zip_path, 'r') as zip_ref:
        zip_ref.extractall(os.path.join(extract_dir, 'conll2003'))
    
    def process_conll_file(file_path):
        sentences = []
        current_sentence = {"tokens": [], "labels": []}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith("-DOCSTART-"):
                    if current_sentence["tokens"]:
                        sentences.append(current_sentence)
                        current_sentence = {"tokens": [], "labels": []}
                else:
                    parts = line.split()
                    if len(parts) >= 4:  # Valid CoNLL format line
                        token = parts[0]
                        label = parts[3]
                        current_sentence["tokens"].append(token)
                        current_sentence["labels"].append(label)
        
        if current_sentence["tokens"]:
            sentences.append(current_sentence)
        
        return sentences

    train_file = os.path.join(extract_dir, "conll2003", "train.txt")
    train_data = process_conll_file(train_file)
    return train_data

def pretrain_on_conll(conll_zip_path):
    print("Pre-training on CoNLL-2003 dataset...")
    tokenizer = AutoTokenizer.from_pretrained(MODEL_PATH)
    model = AutoModelForTokenClassification.from_pretrained(
        MODEL_PATH,
        num_labels=len(CONLL_LABELS),
        id2label=conll_id2label,
        label2id=conll_label2id
    )

    conll_data = load_conll_data(conll_zip_path)
    
    def tokenize_and_align_conll_labels(sentence):
        tokens = sentence["tokens"]
        labels = sentence["labels"]
        
        # Tokenize the words
        word_ids = []
        current_word_idx = None
        tokenized = tokenizer(
            tokens,
            is_split_into_words=True,
            padding="max_length",
            truncation=True,
            max_length=128,
            return_tensors="pt"
        )
        
        # Create label ids
        label_ids = torch.zeros(len(tokenized["input_ids"][0]), dtype=torch.long)
        
        # Map tokens to original words
        word_ids = tokenized.word_ids()
        
        # Assign labels to tokens
        previous_word_idx = None
        for idx, word_idx in enumerate(word_ids):
            if word_idx is None:
                label_ids[idx] = -100
            elif word_idx != previous_word_idx:
                label_ids[idx] = conll_label2id[labels[word_idx]]
            else:
                # For subwords, use the same label as the first token
                if labels[word_idx].startswith("B-"):
                    label_ids[idx] = conll_label2id[f"I-{labels[word_idx][2:]}"]
                else:
                    label_ids[idx] = conll_label2id[labels[word_idx]]
            previous_word_idx = word_idx
        
        return {
            "input_ids": tokenized["input_ids"][0],
            "attention_mask": tokenized["attention_mask"][0],
            "labels": label_ids
        }

    # Process all examples
    tokenized_datasets = [
        tokenize_and_align_conll_labels(sentence)
        for sentence in conll_data
    ]

    # Convert to Dataset
    dataset = Dataset.from_dict({
        "input_ids": torch.stack([x["input_ids"] for x in tokenized_datasets]),
        "attention_mask": torch.stack([x["attention_mask"] for x in tokenized_datasets]),
        "labels": torch.stack([x["labels"] for x in tokenized_datasets])
    })

    training_args = TrainingArguments(
        output_dir="./models/conll_pretrained/",
        num_train_epochs=3,
        per_device_train_batch_size=32,
        learning_rate=5e-5,
        weight_decay=0.01,
        save_strategy="no",
        logging_steps=100,
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
    )

    trainer.train()
    
    # Save the pre-trained model
    model.save_pretrained("./models/conll_pretrained/")
    tokenizer.save_pretrained("./models/conll_pretrained/")
    print("Pre-training completed.")
    return "./models/conll_pretrained/"

def fine_tune_ner_model(train_file, pretrained_model_path=None):
    if pretrained_model_path is None:
        pretrained_model_path = MODEL_PATH
        
    print(f"Fine-tuning using model from: {pretrained_model_path}")
    
    # Load tokenizer
    tokenizer = AutoTokenizer.from_pretrained(pretrained_model_path)
    
    # First load the pre-trained model to get the base layers
    print("Loading pre-trained model...")
    base_model = AutoModelForTokenClassification.from_pretrained(
        pretrained_model_path,
        ignore_mismatched_sizes=True  # Ignore classifier layer mismatch
    )
    
    # Create a new model with our label set
    print("Creating new model with target labels...")
    model = AutoModelForTokenClassification.from_pretrained(
        MODEL_PATH,
        num_labels=len(LABEL_LIST),
        id2label=id2label,
        label2id=label2id
    )
    
    # Copy the pre-trained weights for the base layers
    print("Copying pre-trained weights...")
    model.distilbert = base_model.distilbert
    
    print("Loading training data...")
    with open(train_file, "r") as f:
        train_data = json.load(f)

    def tokenize_and_align_labels(text, entities):
        tokenized = tokenizer(
            text,
            padding="max_length",
            truncation=True,
            max_length=128,
            return_offsets_mapping=True,
            return_tensors="pt"
        )
        
        labels = torch.zeros(len(tokenized["input_ids"][0]), dtype=torch.long)
        offset_mapping = tokenized.offset_mapping[0]
        
        for entity in entities:
            start_char = entity["start"]
            end_char = entity["end"]
            entity_type = entity["label"]
            
            token_start_idx = None
            token_end_idx = None
            
            for idx, (start, end) in enumerate(offset_mapping):
                if start == end == 0:
                    continue
                    
                if start.item() <= start_char and end.item() >= start_char:
                    token_start_idx = idx
                if start.item() <= end_char and end.item() >= end_char:
                    token_end_idx = idx
                    break
            
            if token_start_idx is not None and token_end_idx is not None:
                labels[token_start_idx] = label2id[f"B-{entity_type}"]
                for i in range(token_start_idx + 1, token_end_idx + 1):
                    labels[i] = label2id[f"I-{entity_type}"]
        
        return {
            "input_ids": tokenized["input_ids"][0],
            "attention_mask": tokenized["attention_mask"][0],
            "labels": labels
        }

    print("Processing dataset...")
    tokenized_datasets = [
        tokenize_and_align_labels(item["text"], item["entities"])
        for item in train_data
    ]

    dataset = Dataset.from_dict({
        "input_ids": torch.stack([x["input_ids"] for x in tokenized_datasets]),
        "attention_mask": torch.stack([x["attention_mask"] for x in tokenized_datasets]),
        "labels": torch.stack([x["labels"] for x in tokenized_datasets])
    })

    # Check if CUDA is available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    print(f"Using device: {device}")

    training_args = TrainingArguments(
        output_dir=NER_MODEL_PATH,
        num_train_epochs=5,
        per_device_train_batch_size=8,
        learning_rate=2e-5,
        weight_decay=0.01,
        save_strategy="steps",
        save_steps=100,
        logging_steps=20,
        no_cuda=device == "cpu",
        dataloader_pin_memory=False,  # Disable pin memory for MPS
    )

    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=dataset,
    )

    print("Starting fine-tuning...")
    trainer.train()
    
    print("Saving model...")
    trainer.save_model(NER_MODEL_PATH)
    tokenizer.save_pretrained(NER_MODEL_PATH)
    print("NER model fine-tuned and saved.")

def predict_ner(text, model, tokenizer):
    # Tokenize input
    inputs = tokenizer(
        text,
        padding=True,
        truncation=True,
        max_length=128,
        return_offsets_mapping=True,
        return_tensors="pt"
    )
    
    # Get model predictions
    with torch.no_grad():
        outputs = model(**{k: v for k, v in inputs.items() if k != "offset_mapping"})
    predictions = torch.argmax(outputs.logits, dim=2)[0]
    offset_mapping = inputs["offset_mapping"][0]
    
    # Extract entities
    entities = []
    current_entity = None
    
    for idx, (pred, (start, end)) in enumerate(zip(predictions, offset_mapping)):
        # Skip special tokens
        if start.item() == end.item() == 0:
            continue
            
        label = id2label[pred.item()]
        if label.startswith("B-"):
            if current_entity:
                entities.append(current_entity)
            current_entity = {
                "label": label[2:],
                "text": text[start:end],
                "start": start.item(),
                "end": end.item()
            }
        elif label.startswith("I-") and current_entity:
            if label[2:] == current_entity["label"]:
                current_entity["text"] = text[current_entity["start"]:end]
                current_entity["end"] = end.item()
        elif label == "O":
            if current_entity:
                entities.append(current_entity)
                current_entity = None
    
    if current_entity:
        entities.append(current_entity)
    
    # Clean up entities
    cleaned_entities = []
    for entity in entities:
        text = entity["text"].strip()
        if text:
            entity["text"] = text
            cleaned_entities.append(entity)
    
    # Debug print
    print("\nNER Analysis:")
    print("=" * 50)
    print("Tokens and their predictions:")
    tokens = tokenizer.convert_ids_to_tokens(inputs["input_ids"][0])
    for token, label, (start, end) in zip(tokens, predictions, offset_mapping):
        if start.item() != end.item():
            print(f"{token:15} -> {id2label[label.item()]}")
    print("=" * 50)
    
    return cleaned_entities