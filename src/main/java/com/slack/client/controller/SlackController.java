package com.slack.client.controller;

import com.slack.client.model.ChannelInfo;
import com.slack.client.model.MessageResponse;
import com.slack.client.service.SlackService;
import com.slack.client.service.EmailService;
import com.slack.client.service.SlackSenderService;
import com.slack.client.util.CsvExportUtil;
import jakarta.servlet.ServletRequest;
import org.apache.logging.log4j.core.util.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
public class SlackController {

    private static final Logger logger = LoggerFactory.getLogger(SlackController.class);


    @Autowired
    private SlackService slackService;

    @Autowired
    private CsvExportUtil csvExportUtil;

    @Autowired
    private EmailService emailService;

    @Autowired
    private SlackSenderService slackSenderService;

    @GetMapping(value = "/_status", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<String> ping(ServletRequest p_request) {
        logger.info("[HealthCheckController] Health is getting checked");
        return new ResponseEntity<>(jsonify("status", "ok"), HttpStatus.OK);
    }

    private String jsonify(String field, String value) {
        if (value == null) {
            return "{\"" + field + "\":" + "null}";
        }
        return "{\"" + field + "\":" + "\"" + value + "\"}";
    }

    /**
     * Fetch all public and private channels
     */
    @GetMapping("/channels")
    public ResponseEntity<List<ChannelInfo>> getAllChannels() {
        try {
            List<ChannelInfo> channels = slackService.getAllChannels();
            return ResponseEntity.ok(channels);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages from a specific channel within a date range
     */
    @GetMapping("/messages/{channelId}")
    public ResponseEntity<List<MessageResponse>> getMessagesInDateRange(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            List<MessageResponse> messages = slackService.getMessagesInDateRange(channelId, startDate, endDate, limit);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages from a specific channel for a specific date
     */
    @GetMapping("/messages/{channelId}/date/{date}")
    public ResponseEntity<List<MessageResponse>> getMessagesForDate(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            List<MessageResponse> messages = slackService.getMessagesForDate(channelId, date);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Get count of messages in a specific channel for a specific date
     */
    @GetMapping("/messages/{channelId}/count/{date}")
    public ResponseEntity<Map<String, Object>> getMessageCountForChannelAndDate(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            long count = slackService.getMessageCountForChannelAndDate(channelId, date);
            Map<String, Object> response = new HashMap<>();
            response.put("channel_id", channelId);
            response.put("date", date);
            response.put("message_count", count);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Fetch messages that have replies for a specific date from a channel
     */
    @GetMapping("/messages/{channelId}/replied/{date}")
    public ResponseEntity<List<MessageResponse>> getMessagesWithReplies(
            @PathVariable String channelId,
            @PathVariable String date) {
        try {
            List<MessageResponse> messages = slackService.getMessagesWithReplies(channelId, date);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Export messages in CSV format for a given date range
     */
    @GetMapping("/messages/{channelId}/csv")
    public ResponseEntity<String> exportMessagesAsCsv(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            List<MessageResponse> messages = slackService.getMessagesInDateRange(channelId, startDate, endDate, limit);
            String csvContent = csvExportUtil.exportMessagesToCsv(messages);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment",
                    String.format("slack_messages_%s_to_%s.csv", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate Alert Summary CSV
     */
    @GetMapping("/messages/{channelId}/summary-csv")
    public ResponseEntity<String> generateAlertSummaryCSV(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            String csvContent = slackService.generateAlertSummaryCSV(channelId, startDate, endDate, limit);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment",
                    String.format("alert_summary_%s_to_%s.csv", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate Alert Summary CSV with Service Owner
     */
    @GetMapping("/messages/{channelId}/summary-with-owner-csv")
    public ResponseEntity<String> generateAlertSummaryWithOwnerCSV(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            String csvContent = slackService.generateAlertSummaryWithOwnerCSV(channelId, startDate, endDate, limit);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("text/csv"));
            headers.setContentDispositionFormData("attachment",
                    String.format("alert_summary_with_owner_%s_to_%s.csv", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(csvContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Generate Alert Summary Excel with Service Owner and Conditional Formatting
     */
    @GetMapping("/messages/{channelId}/summary-with-owner-excel")
    public ResponseEntity<byte[]> generateAlertSummaryWithOwnerExcel(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(channelId, startDate, endDate, limit);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            headers.setContentDispositionFormData("attachment",
                    String.format("alert_summary_with_owner_%s_to_%s.xlsx", startDate, endDate));

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(excelContent);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * Send Alert Summary Email on Demand
     */
    @GetMapping("/messages/{channelId}/send-email")
    public ResponseEntity<Map<String, String>> sendAlertSummaryEmail(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "10000") int limit) {
        try {
            emailService.sendAlertSummaryEmail(channelId, startDate, endDate, limit);

            Map<String, String> response = new HashMap<>();
            response.put("status", "SUCCESS");
            response.put("message", String.format("Alert summary email sent successfully for %s to %s", startDate, endDate));
            response.put("channelId", channelId);
            response.put("startDate", startDate);
            response.put("endDate", endDate);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "Failed to send alert summary email: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Send Alert Summary Excel to Slack Channels
     */
    @GetMapping("/messages/{channelId}/send-to-slack")
    public ResponseEntity<Map<String, String>> sendAlertSummaryToSlack(
            @PathVariable String channelId,
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            slackSenderService.sendAlertSummaryToSlack(channelId, startDate, endDate, limit);

            Map<String, String> response = new HashMap<>();
            response.put("status", "SUCCESS");
            response.put("message", String.format("Alert summary Excel sent to Slack channels successfully for %s to %s", startDate, endDate));
            response.put("channelId", channelId);
            response.put("startDate", startDate);
            response.put("endDate", endDate);
            response.put("limit", String.valueOf(limit));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "Failed to send alert summary to Slack: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Send Alert Summary Email based on Source-to-Target Configuration Mappings
     */
    @GetMapping("/alert-summary/send-email-by-mappings")
    public ResponseEntity<Map<String, String>> sendAlertSummaryEmailByMappings(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "10000") int limit) {
        try {
            emailService.sendAlertSummaryBasedOnMappings(startDate, endDate, limit);

            Map<String, String> response = new HashMap<>();
            response.put("status", "SUCCESS");
            response.put("message", String.format("Alert summary emails sent successfully based on mappings for %s to %s", startDate, endDate));
            response.put("startDate", startDate);
            response.put("endDate", endDate);
            response.put("limit", String.valueOf(limit));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "Failed to send alert summary emails based on mappings: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Send Alert Summary to Slack based on Source-to-Target Configuration Mappings
     */
    @GetMapping("/alert-summary/send-slack-by-mappings")
    public ResponseEntity<Map<String, String>> sendAlertSummarySlackByMappings(
            @RequestParam String startDate,
            @RequestParam String endDate,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            slackSenderService.sendAlertSummaryBasedOnMappings(startDate, endDate, limit);

            Map<String, String> response = new HashMap<>();
            response.put("status", "SUCCESS");
            response.put("message", String.format("Alert summary sent to Slack channels successfully based on mappings for %s to %s", startDate, endDate));
            response.put("startDate", startDate);
            response.put("endDate", endDate);
            response.put("limit", String.valueOf(limit));

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> response = new HashMap<>();
            response.put("status", "ERROR");
            response.put("message", "Failed to send alert summary to Slack based on mappings: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    /**
     * Health check endpoint
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> healthCheck() {
        Map<String, String> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "Slack Client API");
        return ResponseEntity.ok(response);
    }
}
