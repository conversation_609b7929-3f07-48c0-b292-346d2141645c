package com.slack.client.service;

import com.slack.client.constants.Constants;
import com.slack.client.model.email.MailAddress;
import com.slack.client.model.email.MailAttachment;
import com.slack.client.model.email.MailContent;
import com.slack.client.model.MessageResponse;
import com.slack.client.util.MailManager;
import com.slack.client.config.ChannelMappingConfig;
import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class EmailService {

    private static final Logger logger = LoggerFactory.getLogger(EmailService.class);

    @Autowired
    private SlackService slackService;

    @Autowired
    private MailManager mailManager;

    @Autowired
    private ChannelMappingConfig channelMappingConfig;

    @Value("${alertSummary.mail.to}")
    private String toEmailsConfig;

    @Value("${alertSummary.mail.channelId}")
    private String defaultChannelId;

    public void sendDailyAlertSummary() {
        try {
            // Calculate yesterday's date
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

            logger.info("Sending daily alert summary for date: {}", yesterdayStr);

            sendAlertSummaryEmail(defaultChannelId, yesterdayStr, yesterdayStr, 10000);

        } catch (Exception e) {
            logger.error("Error sending daily alert summary: {}", e.getMessage(), e);
        }
    }

    /**
     * Send alert summary emails based on source-to-target configuration mappings
     */
    public void sendAlertSummaryBasedOnMappings(String startDate, String endDate, int limit) {
        try {
            logger.info("Starting to send alert summary emails based on source-to-target mappings for date range: {} to {}",
                       startDate, endDate);

            List<ChannelMappingConfig.SourceToTargetMapping> emailMappings = channelMappingConfig.getEmailMappings();

            if (emailMappings.isEmpty()) {
                logger.warn("No email source-to-target mappings configured. Falling back to legacy method.");
                sendDailyAlertSummary();
                return;
            }

            int totalEmailsSent = 0;

            for (ChannelMappingConfig.SourceToTargetMapping mapping : emailMappings) {
                String sourceChannel = mapping.getSource();
                List<String> targetEmails = mapping.getTargets();

                logger.info("Processing email mapping: source={}, targets={}", sourceChannel, targetEmails);

                try {
                    // Generate Excel content for this source channel
                    byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(sourceChannel, startDate, endDate, limit);

                    // Get messages for summary statistics
                    List<MessageResponse> messages = slackService.getMessagesInDateRange(sourceChannel, startDate, endDate, limit);

                    // Generate email content
                    String subject = generateEmailSubject(startDate, endDate);
                    String emailBody = generateEmailBody(messages, startDate, endDate);
                    String fileName = String.format("alert_summary_%s_to_%s.xlsx", startDate, endDate);

                    // Create attachment
                    String base64Content = Base64.encodeBase64String(excelContent);
                    MailAttachment attachment = new MailAttachment(fileName, base64Content,
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");

                    // Create email content
                    List<MailContent> content = Arrays.asList(
                        new MailContent("text/html", emailBody)
                    );

                    // Convert target emails to MailAddress list
                    List<MailAddress> recipients = new ArrayList<>();
                    for (String email : targetEmails) {
                        recipients.add(new MailAddress(email, email)); // Using email as both address and name
                    }

                    // Send email
                    mailManager.sendMailWithAttachment(recipients, subject, content, attachment);
                    totalEmailsSent += targetEmails.size();

                    logger.info("Successfully sent alert summary email from source {} to {} recipients",
                               sourceChannel, targetEmails.size());

                } catch (Exception e) {
                    logger.error("Error processing email mapping for source channel {}: {}", sourceChannel, e.getMessage(), e);
                    // Continue with other mappings even if one fails
                }
            }

            logger.info("Successfully sent alert summary to {} total email recipients across {} source mappings",
                       totalEmailsSent, emailMappings.size());

        } catch (Exception e) {
            logger.error("Error sending alert summary emails based on mappings", e);
            throw new RuntimeException("Error sending alert summary emails based on mappings", e);
        }
    }

    /**
     * Send yesterday's alert summary emails based on source-to-target configuration mappings
     */
    public void sendYesterdayAlertSummaryBasedOnMappings() {
        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

            logger.info("Sending yesterday's alert summary emails based on mappings for date: {}", yesterdayStr);

            sendAlertSummaryBasedOnMappings(yesterdayStr, yesterdayStr, 10000);

        } catch (Exception e) {
            logger.error("Error sending yesterday's alert summary emails based on mappings", e);
            throw new RuntimeException("Error sending yesterday's alert summary emails based on mappings", e);
        }
    }

    public void sendAlertSummaryEmail(String channelId, String startDate, String endDate, int limit) {
        try {
            logger.info("Generating alert summary email for channel: {}, dates: {} to {}", 
                       channelId, startDate, endDate);

            // Generate Excel file
            byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(channelId, startDate, endDate, limit);
            
            // Get messages for summary statistics
            List<MessageResponse> messages = slackService.getMessagesInDateRange(channelId, startDate, endDate, limit);
            
            // Generate email content
            String subject = generateEmailSubject(startDate, endDate);
            String emailBody = generateEmailBody(messages, startDate, endDate);
            String fileName = String.format("alert_summary_%s_to_%s.xlsx", startDate, endDate);
            
            // Create attachment
            String base64Content = Base64.encodeBase64String(excelContent);
            MailAttachment attachment = new MailAttachment(fileName, base64Content, 
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            
            // Parse recipient list
            List<MailAddress> recipients = parseEmailRecipients(toEmailsConfig);
            
            // Create email content
            List<MailContent> content = Arrays.asList(
                new MailContent("text/html", emailBody)
            );
            
            // Send email
            mailManager.sendMailWithAttachment(recipients, subject, content, attachment);
            
            logger.info("Alert summary email sent successfully for dates: {} to {}", startDate, endDate);
            
        } catch (Exception e) {
            logger.error("Error sending alert summary email: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to send alert summary email", e);
        }
    }

    private String generateEmailSubject(String startDate, String endDate) {
        return String.format("Alert Summary for %s to %s", startDate, endDate);
    }

    private String generateEmailBody(List<MessageResponse> messages, String startDate, String endDate) {
        // Calculate statistics
        Map<String, Integer> priorityCount = new HashMap<>();
        Set<String> uniqueAlerts = new HashSet<>();
        int totalOccurrences = 0;

        for (MessageResponse message : messages) {
            String alertName = extractAlertNameFromTitle(message.getMessageTitle());
            uniqueAlerts.add(alertName);
            totalOccurrences++;
            
            String priority = message.getPriority();
            if (priority != null && !priority.trim().isEmpty()) {
                priorityCount.put(priority, priorityCount.getOrDefault(priority, 0) + 1);
            } else {
                priorityCount.put(Constants.DEFAULT_PRIORITY, priorityCount.getOrDefault(Constants.DEFAULT_PRIORITY, 0) + 1);
            }
        }

        // Build email body
        StringBuilder body = new StringBuilder();
        body.append("<html><body>");
        body.append("<h2>Alert Summary Report</h2>");
        body.append("<p><strong>Period:</strong> ").append(startDate).append(" to ").append(endDate).append("</p>");
        body.append("<br>");
        
        body.append("<h3>Summary Statistics:</h3>");
        body.append("<ul>");
        body.append("<li><strong>Number of Unique Alerts:</strong> ").append(uniqueAlerts.size()).append("</li>");
        body.append("<li><strong>Total Occurrences:</strong> ").append(totalOccurrences).append("</li>");
        body.append("</ul>");
        
        body.append("<h3>Priority-wise Alert Count:</h3>");
        body.append("<ul>");
        for (Map.Entry<String, Integer> entry : priorityCount.entrySet()) {
            body.append("<li><strong>").append(entry.getKey()).append(":</strong> ")
                .append(entry.getValue()).append(" times</li>");
        }
        body.append("</ul>");
        
        body.append("<br>");
        body.append("<p>Please find the detailed alert summary in the attached Excel file.</p>");
        body.append("<p>The Excel file includes an Action Item column with color-coded recommendations:</p>");
        body.append("<ul>");
        body.append("<li><span style='background-color: orange; padding: 2px;'>Orange \"Actionable\"</span> - P0 unresolved alerts with no replies</li>");
        body.append("<li><span style='background-color: #FFB347; padding: 2px;'>Light Orange \"Actionable\"</span> - P1 unresolved alerts with no replies</li>");
        body.append("<li><span style='background-color: #FFBF00; padding: 2px;'>Amber \"Tune\"</span> - P0 resolved alerts with no replies</li>");
        body.append("<li><span style='background-color: lightgreen; padding: 2px;'>Green \"Review and Tune\"</span> - P1 resolved alerts with no replies</li>");
        body.append("<li><span style='background-color: skyblue; padding: 2px;'>Sky Blue \"Review\"</span> - All other alerts</li>");
        body.append("</ul>");
        
        body.append("<br>");
        body.append("<p>Best regards,<br>BBPS Dev Team</p>");
        body.append("</body></html>");
        
        return body.toString();
    }

    private String extractAlertNameFromTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "Unknown Alert";
        }
        
        String cleanTitle = title.trim();
        if (cleanTitle.startsWith("\"") && cleanTitle.endsWith("\"")) {
            cleanTitle = cleanTitle.substring(1, cleanTitle.length() - 1);
        }
        
        Pattern firingPattern = Pattern.compile("^\\[FIRING:\\d+\\]\\s*(.+)$");
        Pattern resolvedPattern = Pattern.compile("^\\[RESOLVED\\]\\s*(.+)$");
        
        Matcher firingMatcher = firingPattern.matcher(cleanTitle);
        if (firingMatcher.find()) {
            return firingMatcher.group(1).trim();
        }
        
        Matcher resolvedMatcher = resolvedPattern.matcher(cleanTitle);
        if (resolvedMatcher.find()) {
            return resolvedMatcher.group(1).trim();
        }
        
        return cleanTitle;
    }

    private List<MailAddress> parseEmailRecipients(String emailConfig) {
        List<MailAddress> recipients = new ArrayList<>();
        
        // Parse format: {'<EMAIL>':'Name1','<EMAIL>':'Name2'}
        Pattern pattern = Pattern.compile("'([^']+)'\\s*:\\s*'([^']+)'");
        Matcher matcher = pattern.matcher(emailConfig);
        
        while (matcher.find()) {
            String email = matcher.group(1);
            String name = matcher.group(2);
            recipients.add(new MailAddress(email, name));
        }
        
        if (recipients.isEmpty()) {
            logger.warn("No valid email recipients found in config: {}", emailConfig);
            // Fallback to a default recipient
            recipients.add(new MailAddress("<EMAIL>", "Saurabh Kamal"));
        }
        
        return recipients;
    }
}
