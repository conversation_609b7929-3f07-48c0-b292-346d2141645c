package com.slack.client.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;

@Service
public class SlackSenderScheduler {

    private static final Logger logger = LoggerFactory.getLogger(SlackSenderScheduler.class);

    @Autowired
    private SlackSenderService slackSenderService;

    /**
     * Scheduled method to send daily alert summary to Slack channels at 10:00 AM every day
     * Cron expression: "0 0 10 * * ?" means:
     * - 0 seconds
     * - 0 minutes
     * - 10 hours (10 AM)
     * - every day of month
     * - every month
     * - every day of week
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void sendDailyAlertSummaryToSlack() {
        logger.info("Starting scheduled daily alert summary Slack task at 10:00 AM");

        try {
            // Use new configuration-based method
            slackSenderService.sendYesterdayAlertSummaryBasedOnMappings();
            logger.info("Daily alert summary Slack task completed successfully");
        } catch (Exception e) {
            logger.error("Error in scheduled daily alert summary Slack task: {}", e.getMessage(), e);
        }
    }

    /**
     * Scheduled method to send weekly alert summary to Slack channels every Monday at 9:45 AM
     * Covers weekend period: Friday to Sunday
     * Cron expression: "0 45 9 * * MON" means:
     * - 0 seconds
     * - 45 minutes
     * - 9 hours (9 AM)
     * - every day of month
     * - every month
     * - Monday only
     */
    @Scheduled(cron = "0 45 9 * * MON")
    public void sendWeeklyAlertSummaryToSlack() {
        logger.info("Starting scheduled weekly alert summary Slack task at 9:45 AM on Monday");

        try {
            // Calculate weekend dates (Friday to Sunday)
            LocalDate today = LocalDate.now(); // This will be Monday when scheduler runs
            LocalDate friday = today.minusDays(3); // Monday - 3 days = Friday
            LocalDate sunday = today.minusDays(1); // Monday - 1 day = Sunday

            String startDate = friday.format(DateTimeFormatter.ISO_LOCAL_DATE);
            String endDate = sunday.format(DateTimeFormatter.ISO_LOCAL_DATE);

            logger.info("Generating weekly alert summary for weekend period: {} to {}", startDate, endDate);

            // Use configuration-based method with higher limit for weekend data
            slackSenderService.sendAlertSummaryBasedOnMappings(startDate, endDate, 50000);

            logger.info("Weekly alert summary Slack task completed successfully for period: {} to {}", startDate, endDate);
        } catch (Exception e) {
            logger.error("Error in scheduled weekly alert summary Slack task: {}", e.getMessage(), e);
        }
    }
}
