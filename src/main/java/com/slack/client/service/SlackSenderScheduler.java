package com.slack.client.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class SlackSenderScheduler {

    private static final Logger logger = LoggerFactory.getLogger(SlackSenderScheduler.class);

    @Autowired
    private SlackSenderService slackSenderService;

    /**
     * Scheduled method to send daily alert summary to Slack channels at 9:00 AM every day
     * Cron expression: "0 0 9 * * ?" means:
     * - 0 seconds
     * - 0 minutes
     * - 9 hours (9 AM)
     * - every day of month
     * - every month
     * - every day of week
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void sendDailyAlertSummaryToSlack() {
        logger.info("Starting scheduled daily alert summary Slack task at 9:00 AM");

        try {
            // Use new configuration-based method
            slackSenderService.sendYesterdayAlertSummaryBasedOnMappings();
            logger.info("Daily alert summary Slack task completed successfully");
        } catch (Exception e) {
            logger.error("Error in scheduled daily alert summary Slack task: {}", e.getMessage(), e);
        }
    }
}
