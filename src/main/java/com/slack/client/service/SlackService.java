package com.slack.client.service;

import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.request.conversations.ConversationsHistoryRequest;
import com.slack.api.methods.request.conversations.ConversationsListRequest;
import com.slack.api.methods.response.conversations.ConversationsHistoryResponse;
import com.slack.api.methods.response.conversations.ConversationsListResponse;
import com.slack.api.model.Conversation;
import com.slack.api.model.Message;
import com.slack.client.config.InstanceOwnerConfig;
import com.slack.client.model.ChannelInfo;
import com.slack.client.model.MessageResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import com.slack.client.util.LegendSheetGenerator;

@Service
public class SlackService {

    private static final Logger logger = LoggerFactory.getLogger(SlackService.class);

    // Inner class to hold parsed alert message parts
    private static class AlertMessageParts {
        String title;
        String summary;
        String description;
        String priority;
        String service;

        AlertMessageParts(String title, String summary, String description, String priority, String service) {
            this.title = title;
            this.summary = summary;
            this.description = description;
            this.priority = priority;
            this.service = service;
        }
    }

    @Autowired
    private MethodsClient slackClient;

    @Autowired
    private InstanceOwnerConfig instanceOwnerConfig;

    @Autowired
    private LegendSheetGenerator legendSheetGenerator;

    public List<ChannelInfo> getAllChannels() {
        try {
            ConversationsListRequest request = ConversationsListRequest.builder()
                    .types(List.of(
                        com.slack.api.model.ConversationType.PUBLIC_CHANNEL,
                        com.slack.api.model.ConversationType.PRIVATE_CHANNEL
                    ))
                    .limit(1000)
                    .build();

            ConversationsListResponse response = slackClient.conversationsList(request);

            if (!response.isOk()) {
                throw new RuntimeException("Failed to fetch channels: " + response.getError());
            }

            return response.getChannels().stream()
                    .map(this::convertToChannelInfo)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("Error fetching channels", e);
            throw new RuntimeException("Error fetching channels", e);
        }
    }

    public List<MessageResponse> getMessagesInDateRange(String channelId, String startDate, String endDate) {
        return getMessagesInDateRange(channelId, startDate, endDate, 1000); // Default limit
    }

    public List<MessageResponse> getMessagesInDateRange(String channelId, String startDate, String endDate, int limit) {
        try {
            long startTimestamp = convertDateToTimestamp(startDate);
            long endTimestamp = convertDateToTimestamp(endDate) + 86400; // Add 24 hours to include end date

            logger.info("Fetching messages for channel {} from {} to {} with limit {}", channelId, startTimestamp, endTimestamp, limit);

            List<MessageResponse> allMessages = new ArrayList<>();
            String cursor = null;
            int remainingLimit = limit;

            do {
                // Slack API has a maximum limit of 1000 per request
                int requestLimit = Math.min(remainingLimit, 1000);

                ConversationsHistoryRequest.ConversationsHistoryRequestBuilder requestBuilder = ConversationsHistoryRequest.builder()
                        .channel(channelId)
                        .oldest(String.valueOf(startTimestamp))
                        .latest(String.valueOf(endTimestamp))
                        .limit(requestLimit);

                if (cursor != null) {
                    requestBuilder.cursor(cursor);
                }

                ConversationsHistoryRequest request = requestBuilder.build();
                ConversationsHistoryResponse response = slackClient.conversationsHistory(request);

                if (!response.isOk()) {
                    String error = response.getError();
                    logger.warn("Failed to fetch messages from channel {}: {}", channelId, error);

                    // If bot is not in channel, return empty list instead of throwing exception
                    if ("not_in_channel".equals(error) || "channel_not_found".equals(error)) {
                        logger.info("Bot is not a member of channel {} or channel not found, returning empty list", channelId);
                        return new ArrayList<>();
                    }

                    throw new RuntimeException("Failed to fetch messages: " + error);
                }

                List<MessageResponse> batchMessages = response.getMessages().stream()
                        .map(message -> convertToMessageResponse(message, channelId))
                        .collect(Collectors.toList());

                allMessages.addAll(batchMessages);
                remainingLimit -= batchMessages.size();

                // Update cursor for next iteration
                cursor = response.getResponseMetadata() != null ? response.getResponseMetadata().getNextCursor() : null;

                logger.debug("Retrieved {} messages in this batch, total so far: {}", batchMessages.size(), allMessages.size());

                // Continue if we have more messages to fetch and haven't reached the limit
            } while (cursor != null && !cursor.isEmpty() && remainingLimit > 0);

            logger.info("Retrieved total {} messages from channel {}", allMessages.size(), channelId);

            return allMessages;

        } catch (Exception e) {
            logger.error("Error fetching messages for date range from channel {}", channelId, e);
            throw new RuntimeException("Error fetching messages for date range", e);
        }
    }

    public List<MessageResponse> getMessagesForDate(String channelId, String date) {
        return getMessagesInDateRange(channelId, date, date);
    }

    public long getMessageCountForChannelAndDate(String channelId, String date) {
        try {
            List<MessageResponse> messages = getMessagesForDate(channelId, date);
            return messages.size();
        } catch (Exception e) {
            logger.error("Error counting messages for channel {} and date {}", channelId, date, e);
            throw new RuntimeException("Error counting messages for channel and date", e);
        }
    }

    public List<MessageResponse> getMessagesWithReplies(String channelId, String date) {
        List<MessageResponse> allMessages = getMessagesForDate(channelId, date);
        return allMessages.stream()
                .filter(MessageResponse::isHasReplies)
                .collect(Collectors.toList());
    }

    private ChannelInfo convertToChannelInfo(Conversation conversation) {
        // Use basic properties that are available
        String name = conversation.getName() != null ? conversation.getName() : conversation.getId();

        // For now, use simple defaults - we can enhance this later
        // The main functionality will work with ID and name
        return new ChannelInfo(
                conversation.getId(),
                name,
                false, // isPrivate - default to false
                true,  // isChannel - default to true
                false, // isGroup
                false, // isIm
                false, // isMpim
                0      // memberCount - default to 0
        );
    }

    private MessageResponse convertToMessageResponse(Message message, String channelId) {
        // Handle null values safely
        String messageText = extractMessageText(message);
        String userId = message.getUser() != null ? message.getUser() : null;
        String messageType = message.getType() != null ? message.getType() : "message";
        String timestamp = message.getTs() != null ? message.getTs() : "";

        // If user is null but we have botId or username, use those
        if (userId == null) {
            if (message.getBotId() != null) {
                userId = message.getBotId();
            } else if (message.getUsername() != null) {
                userId = message.getUsername();
            }
        }

        // Get username - optimized for performance (no API calls)
        String username = getOptimizedUsername(userId, message);

        // Check if message has replies
        boolean hasReplies = message.getReplyCount() != null && message.getReplyCount() > 0;
        int replyCount = message.getReplyCount() != null ? message.getReplyCount() : 0;

        // Parse the message text into structured fields
        AlertMessageParts parts = parseAlertMessage(messageText);

        return new MessageResponse(
                MessageResponse.formatSlackTimestamp(timestamp),
                parts.title,
                parts.summary,
                parts.description,
                parts.priority,
                parts.service,
                hasReplies,
                replyCount,
                userId,
                username,
                channelId,
                messageType
        );
    }

    private String extractMessageText(Message message) {
        // First try the standard text field
        if (message.getText() != null && !message.getText().trim().isEmpty()) {
            return message.getText();
        }

        // For bot messages, try to extract from attachments
        if (message.getAttachments() != null && !message.getAttachments().isEmpty()) {
            StringBuilder textBuilder = new StringBuilder();
            for (var attachment : message.getAttachments()) {
                if (attachment.getText() != null && !attachment.getText().trim().isEmpty()) {
                    textBuilder.append(attachment.getText()).append(" ");
                }
                if (attachment.getTitle() != null && !attachment.getTitle().trim().isEmpty()) {
                    textBuilder.append("[Title: ").append(attachment.getTitle()).append("] ");
                }
                if (attachment.getPretext() != null && !attachment.getPretext().trim().isEmpty()) {
                    textBuilder.append(attachment.getPretext()).append(" ");
                }
                if (attachment.getFallback() != null && !attachment.getFallback().trim().isEmpty()) {
                    textBuilder.append(attachment.getFallback()).append(" ");
                }
            }
            String attachmentText = textBuilder.toString().trim();
            if (!attachmentText.isEmpty()) {
                return attachmentText;
            }
        }

        // For bot messages, try to extract from blocks (newer Slack format)
        if (message.getBlocks() != null && !message.getBlocks().isEmpty()) {
            StringBuilder textBuilder = new StringBuilder();
            for (var block : message.getBlocks()) {
                String blockText = extractTextFromBlock(block);
                if (blockText != null && !blockText.isEmpty()) {
                    textBuilder.append(blockText).append(" ");
                }
            }
            String blockText = textBuilder.toString().trim();
            if (!blockText.isEmpty()) {
                return blockText;
            }
        }

        // Create a meaningful summary for bot messages
        return createBotMessageSummary(message);
    }

    private String createBotMessageSummary(Message message) {
        StringBuilder summary = new StringBuilder();

        // Add bot information
        if (message.getUsername() != null) {
            summary.append("[").append(message.getUsername()).append(" Bot] ");
        } else if (message.getBotId() != null) {
            summary.append("[Bot ").append(message.getBotId()).append("] ");
        } else {
            summary.append("[System Message] ");
        }

        // Add timestamp info
        if (message.getTs() != null) {
            summary.append("Message at ").append(MessageResponse.formatSlackTimestamp(message.getTs()));
        }

        // Add subtype information if available
        if (message.getSubtype() != null) {
            summary.append(" (").append(message.getSubtype()).append(")");
        }

        // Add file information if present
        if (message.getFiles() != null && !message.getFiles().isEmpty()) {
            summary.append(" - Contains ").append(message.getFiles().size()).append(" file(s)");
        }

        // Add reaction information if present
        if (message.getReactions() != null && !message.getReactions().isEmpty()) {
            summary.append(" - Has ").append(message.getReactions().size()).append(" reaction(s)");
        }

        String result = summary.toString().trim();
        return result.isEmpty() ? "[Empty bot message]" : result;
    }

    private String extractTextFromBlock(Object block) {
        // This is a simplified implementation
        // In a real implementation, you'd need to parse the block structure properly
        if (block != null) {
            String blockStr = block.toString();
            // Try to extract any text content from the block string representation
            if (blockStr.contains("text")) {
                // This is a very basic extraction - you'd want to properly parse JSON here
                return "[Block content]";
            }
        }
        return null;
    }

    private String getOptimizedUsername(String userId, Message message) {
        if (userId == null) return "Unknown";

        // If it's a bot, use the bot username directly if available, otherwise use bot ID
        if (message.getUsername() != null) {
            return message.getUsername();
        }

        // For bot IDs, just return the bot ID to avoid slow API calls
        if (userId.startsWith("B0")) { // Bot IDs typically start with B0
            return userId; // Return bot ID as username to avoid slow API calls
        }

        // For real users, we could make API calls, but for performance, just return user ID
        return userId; // Return user ID for performance
    }

    /**
     * Parse AlertManager message text into structured components
     */
    private AlertMessageParts parseAlertMessage(String messageText) {
        if (messageText == null || messageText.trim().isEmpty()) {
            return new AlertMessageParts("Unknown Alert", "No summary available", "No description available", "Normal", "NA");
        }

        String title = extractTitle(messageText);
        String summary = extractSummary(messageText);
        String description = extractDescription(messageText);
        String priority = extractPriority(messageText);
        String service = extractService(messageText);

        return new AlertMessageParts(title, summary, description, priority, service);
    }

    private String extractTitle(String messageText) {
        // Look for title in the format: [Title: [FIRING:1] alert-name || ...]
        Pattern titlePattern = Pattern.compile("\\[Title: (\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*[^\\|]+)");
        Matcher matcher = titlePattern.matcher(messageText);

        if (matcher.find()) {
            String title = matcher.group(1).trim();
            return title;
        }

        // Fallback: Look for alert patterns at the end of the message
        Pattern endAlertPattern = Pattern.compile("\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*([^\\|\\n]+)(?:\\s*\\|\\|.*)?$");
        matcher = endAlertPattern.matcher(messageText);

        if (matcher.find()) {
            String fullMatch = matcher.group(0).trim();
            // Remove everything after ||
            String cleanTitle = fullMatch.replaceAll("\\s*\\|\\|.*", "").trim();
            return cleanTitle;
        }

        // Another fallback: Look for any alert pattern in the message
        Pattern anyAlertPattern = Pattern.compile("\\[(FIRING|RESOLVED|WARNING)[^\\]]*\\]\\s*([a-zA-Z0-9_-]+(?:-[a-zA-Z0-9_-]+)*)");
        matcher = anyAlertPattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(0).trim();
        }

        // Final fallback: extract from techteam and severity
        String techteam = extractField(messageText, "techteam");
        String severity = extractField(messageText, "severity");

        if (!techteam.isEmpty() && !severity.isEmpty()) {
            return techteam + " - " + severity + " alert";
        }

        return "Alert Notification";
    }

    private String extractSummary(String messageText) {
        // Look for summary in annotations
        Pattern summaryPattern = Pattern.compile("- summary = ([^\\n]+)");
        Matcher matcher = summaryPattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // Fallback: use first line of description if available
        String description = extractField(messageText, "description");
        if (!description.isEmpty()) {
            String[] lines = description.split("\\.");
            if (lines.length > 0) {
                return lines[0].trim();
            }
        }

        return "Alert summary not available";
    }

    private String extractDescription(String messageText) {
        // Look for description in annotations
        Pattern descPattern = Pattern.compile("- description = ([^\\n]+(?:\\n[^-][^\\n]*)*)");
        Matcher matcher = descPattern.matcher(messageText);

        if (matcher.find()) {
            String desc = matcher.group(1).trim();
            // Clean up the description by removing URLs and extra formatting
            desc = desc.replaceAll("https?://[^\\s]+", "[URL]");
            desc = desc.replaceAll("\\s+", " ");
            return desc.length() > 200 ? desc.substring(0, 200) + "..." : desc;
        }

        // Fallback: extract labels info
        String techteam = extractField(messageText, "techteam");
        String severity = extractField(messageText, "severity");

        if (!techteam.isEmpty() || !severity.isEmpty()) {
            return String.format("Alert from team: %s, severity: %s",
                                techteam.isEmpty() ? "unknown" : techteam,
                                severity.isEmpty() ? "unknown" : severity);
        }

        return "Alert description not available";
    }

    private String extractPriority(String messageText) {
        // Look for priority in annotations first (- priority = P1/P2/P3)
        Pattern priorityPattern = Pattern.compile("- priority = (P[0-9]+)");
        Matcher matcher = priorityPattern.matcher(messageText);

        if (matcher.find()) {
            String priority = matcher.group(1).trim().toUpperCase();
            return priority; // Return the actual priority value (P0, P1, P2, P3)
        }

        // Look for priority patterns anywhere in the message (P0, P1, P2, P3)
        Pattern anyPriorityPattern = Pattern.compile("\\b(P[0-3])\\b");
        matcher = anyPriorityPattern.matcher(messageText);

        if (matcher.find()) {
            String priority = matcher.group(1).toUpperCase();
            return priority; // Return the actual priority value (P0, P1, P2, P3)
        }

        // Fallback: determine from severity and return a descriptive value
        String severity = extractField(messageText, "severity");
        switch (severity.toLowerCase()) {
            case "critical": return "Critical";
            case "warning": return "Warning";
            case "error": return "Error";
            default: return "Normal";
        }
    }

    private String extractService(String messageText) {
        // Look for service in Labels section first
        Pattern labelsServicePattern = Pattern.compile("Labels:.*?service:\\s*([^|\\n\\s]+)");
        Matcher matcher = labelsServicePattern.matcher(messageText);

        if (matcher.find()) {
            String service = matcher.group(1).trim();
            // Remove any trailing punctuation or special characters
            service = service.replaceAll("[|\\s]+$", "");
            return service.isEmpty() ? "NA" : service;
        }

        // Look for service in the main alert line (before ||)
        Pattern mainLineServicePattern = Pattern.compile("\\[FIRING:[0-9]+\\]\\s+[^\\s]+\\s+[^\\s]+\\s+([^\\s|]+)");
        matcher = mainLineServicePattern.matcher(messageText);

        if (matcher.find()) {
            String service = matcher.group(1).trim();
            return service.isEmpty() ? "NA" : service;
        }

        // Look for service field anywhere in the message
        String serviceField = extractField(messageText, "service");
        if (!serviceField.isEmpty()) {
            return serviceField;
        }

        return "NA";
    }

    private String extractField(String messageText, String fieldName) {
        Pattern pattern = Pattern.compile(fieldName + ":\\s*([^|\\n]+)");
        Matcher matcher = pattern.matcher(messageText);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        return "";
    }

    public String generateAlertSummaryCSV(String channelId, String startDate, String endDate, int limit) {
        try {
            // Get messages using existing method
            List<MessageResponse> messages = getMessagesInDateRange(channelId, startDate, endDate, limit);

            // Generate alert summary using the existing logic
            return generateAlertSummaryFromMessages(messages);

        } catch (Exception e) {
            logger.error("Error generating alert summary for channel {} from {} to {}", channelId, startDate, endDate, e);
            throw new RuntimeException("Error generating alert summary", e);
        }
    }

    private String generateAlertSummaryFromMessages(List<MessageResponse> messages) {
        Map<String, AlertInfo> alertMap = new LinkedHashMap<>();

        // Process each message
        for (MessageResponse message : messages) {
            String messageTitle = message.getMessageTitle();
            String priority = message.getPriority();
            String service = message.getService();
            boolean hasReplies = message.isHasReplies();
            int numReplies = message.getReplyCount();
            String summary = message.getMessageSummary();

            String alertName = extractAlertNameFromTitle(messageTitle);
            boolean isResolved = messageTitle.contains("[RESOLVED]");

            AlertInfo info = alertMap.getOrDefault(alertName, new AlertInfo());
            info.alertName = alertName;
            info.occurrences++;
            info.priority = info.priority == null ? priority : info.priority;
            info.service = info.service == null ? service : info.service;
            info.description = info.description == null ? summary : info.description;
            info.resolved = info.resolved || isResolved;
            info.hasReplies = info.hasReplies || hasReplies;
            info.totalReplies += numReplies;

            // Track per-alert statistics
            if (hasReplies) {
                info.alertInstancesWithReplies++;
            }
            if (numReplies > info.maxRepliesOnThisAlert) {
                info.maxRepliesOnThisAlert = numReplies;
            }

            alertMap.put(alertName, info);
        }

        // Generate CSV content
        StringBuilder csvContent = new StringBuilder();

        // Add header
        csvContent.append("\"Alert Name\",\"Occurrences\",\"Resolved_Same_Day\",\"Priority\",\"Service\",")
                  .append("\"Has_Replies\",\"Number_of_Replies\",\"Description\",")
                  .append("\"Alert_Types_With_Replies\",\"Max_Replies_On_Alert_Type\"\n");

        // Add data rows
        for (AlertInfo info : alertMap.values()) {
            csvContent.append("\"").append(escapeCSV(info.alertName)).append("\",")
                      .append("\"").append(info.occurrences).append("\",")
                      .append("\"").append(info.resolved).append("\",")
                      .append("\"").append(escapeCSV(info.priority)).append("\",")
                      .append("\"").append(escapeCSV(info.service)).append("\",")
                      .append("\"").append(info.hasReplies).append("\",")
                      .append("\"").append(info.totalReplies).append("\",")
                      .append("\"").append(escapeCSV(info.description)).append("\",")
                      .append("\"").append(info.alertInstancesWithReplies).append("\",")
                      .append("\"").append(info.maxRepliesOnThisAlert).append("\"\n");
        }

        return csvContent.toString();
    }

    private String extractAlertNameFromTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "Unknown Alert";
        }

        String cleanTitle = title.trim();

        // Remove any leading/trailing quotes if present
        if (cleanTitle.startsWith("\"") && cleanTitle.endsWith("\"")) {
            cleanTitle = cleanTitle.substring(1, cleanTitle.length() - 1);
        }

        // Extract the base alert name by removing only [FIRING:X] or [RESOLVED] prefixes
        // Be very specific to avoid incorrect grouping
        Pattern firingPattern = Pattern.compile("^\\[FIRING:\\d+\\]\\s*(.+)$");
        Pattern resolvedPattern = Pattern.compile("^\\[RESOLVED\\]\\s*(.+)$");

        Matcher firingMatcher = firingPattern.matcher(cleanTitle);
        if (firingMatcher.find()) {
            return firingMatcher.group(1).trim();
        }

        Matcher resolvedMatcher = resolvedPattern.matcher(cleanTitle);
        if (resolvedMatcher.find()) {
            return resolvedMatcher.group(1).trim();
        }

        // If no pattern matches, return the clean title
        return cleanTitle;
    }

    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        // Escape quotes by doubling them
        return value.replace("\"", "\"\"");
    }

    // Inner class for alert information (same as in AlertSummaryGenerator)
    private static class AlertInfo {
        String alertName;
        String priority;
        String description;
        String service;
        int occurrences = 0;
        boolean resolved = false;
        boolean hasReplies = false;
        int totalReplies = 0;
        int maxRepliesOnThisAlert = 0;
        int alertInstancesWithReplies = 0;
    }

    public String generateAlertSummaryWithOwnerCSV(String channelId, String startDate, String endDate, int limit) {
        try {
            // Get messages using existing method
            List<MessageResponse> messages = getMessagesInDateRange(channelId, startDate, endDate, limit);

            // Generate alert summary with service owner using the existing logic
            return generateAlertSummaryWithOwnerFromMessages(messages);

        } catch (Exception e) {
            logger.error("Error generating alert summary with owner for channel {} from {} to {}", channelId, startDate, endDate, e);
            throw new RuntimeException("Error generating alert summary with owner", e);
        }
    }

    private String generateAlertSummaryWithOwnerFromMessages(List<MessageResponse> messages) {
        Map<String, AlertInfoWithOwner> alertMap = new LinkedHashMap<>();

        // Process each message
        for (MessageResponse message : messages) {
            String messageTitle = message.getMessageTitle();
            String priority = message.getPriority();
            String service = message.getService();
            boolean hasReplies = message.isHasReplies();
            int numReplies = message.getReplyCount();
            String summary = message.getMessageSummary();

            String alertName = extractAlertNameFromTitle(messageTitle);
            boolean isResolved = messageTitle.contains("[RESOLVED]");

            // Determine service owner using the configuration
            String serviceOwner = instanceOwnerConfig.determineOwner(alertName, summary);

            AlertInfoWithOwner info = alertMap.getOrDefault(alertName, new AlertInfoWithOwner());
            info.alertName = alertName;
            info.occurrences++;
            info.priority = info.priority == null ? priority : info.priority;
            info.service = info.service == null ? service : info.service;
            info.description = info.description == null ? summary : info.description;
            info.resolved = info.resolved || isResolved;
            info.hasReplies = info.hasReplies || hasReplies;
            info.totalReplies += numReplies;
            info.serviceOwner = serviceOwner;

            // Track per-alert statistics
            if (hasReplies) {
                info.alertInstancesWithReplies++;
            }
            if (numReplies > info.maxRepliesOnThisAlert) {
                info.maxRepliesOnThisAlert = numReplies;
            }

            alertMap.put(alertName, info);
        }

        // Generate CSV content
        StringBuilder csvContent = new StringBuilder();

        // Add header with Service and Service Owner columns
        csvContent.append("\"Alert Name\",\"Occurrences\",\"Resolved_Same_Day\",\"Priority\",\"Service\",")
                  .append("\"Has_Replies\",\"Number_of_Replies\",\"Description\",")
                  .append("\"Alert_Types_With_Replies\",\"Max_Replies_On_Alert_Type\",\"Service Owner\"\n");

        // Add data rows
        for (AlertInfoWithOwner info : alertMap.values()) {
            csvContent.append("\"").append(escapeCSV(info.alertName)).append("\",")
                      .append("\"").append(info.occurrences).append("\",")
                      .append("\"").append(info.resolved).append("\",")
                      .append("\"").append(escapeCSV(info.priority)).append("\",")
                      .append("\"").append(escapeCSV(info.service)).append("\",")
                      .append("\"").append(info.hasReplies).append("\",")
                      .append("\"").append(info.totalReplies).append("\",")
                      .append("\"").append(escapeCSV(info.description)).append("\",")
                      .append("\"").append(info.alertInstancesWithReplies).append("\",")
                      .append("\"").append(info.maxRepliesOnThisAlert).append("\",")
                      .append("\"").append(escapeCSV(info.serviceOwner)).append("\"\n");
        }

        return csvContent.toString();
    }

    // Inner class for alert information with service owner
    private static class AlertInfoWithOwner {
        String alertName;
        String priority;
        String description;
        String service;
        String serviceOwner;
        int occurrences = 0;
        boolean resolved = false;
        boolean hasReplies = false;
        int totalReplies = 0;
        int maxRepliesOnThisAlert = 0;
        int alertInstancesWithReplies = 0;
    }

    private long convertDateToTimestamp(String dateStr) {
        try {
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ISO_LOCAL_DATE);
            LocalDateTime dateTime = date.atStartOfDay();
            return dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid date format. Use YYYY-MM-DD", e);
        }
    }

    public byte[] generateAlertSummaryWithOwnerExcel(String channelId, String startDate, String endDate, int limit) {
        try {
            // Get messages using existing method
            List<MessageResponse> messages = getMessagesInDateRange(channelId, startDate, endDate, limit);

            // Generate Excel file with conditional formatting
            return generateAlertSummaryWithOwnerExcelFromMessages(messages, startDate, endDate);

        } catch (Exception e) {
            logger.error("Error generating alert summary Excel with owner for channel {} from {} to {}", channelId, startDate, endDate, e);
            throw new RuntimeException("Error generating alert summary Excel with owner", e);
        }
    }

    private byte[] generateAlertSummaryWithOwnerExcelFromMessages(List<MessageResponse> messages, String startDate, String endDate) {
        try (XSSFWorkbook workbook = new XSSFWorkbook()) {
            // Create Alert Summary sheet
            Sheet sheet = workbook.createSheet("Alert Summary");

            // Create header style
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setColor(IndexedColors.WHITE.getIndex());
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);

            // Create normal style for data cells
            CellStyle normalStyle = workbook.createCellStyle();
            normalStyle.setBorderBottom(BorderStyle.THIN);
            normalStyle.setBorderTop(BorderStyle.THIN);
            normalStyle.setBorderRight(BorderStyle.THIN);
            normalStyle.setBorderLeft(BorderStyle.THIN);

            // Create action item styles
            CellStyle orangeActionStyle = workbook.createCellStyle();
            orangeActionStyle.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
            orangeActionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            orangeActionStyle.setBorderBottom(BorderStyle.THIN);
            orangeActionStyle.setBorderTop(BorderStyle.THIN);
            orangeActionStyle.setBorderRight(BorderStyle.THIN);
            orangeActionStyle.setBorderLeft(BorderStyle.THIN);

            CellStyle lightOrangeActionStyle = workbook.createCellStyle();
            lightOrangeActionStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
            lightOrangeActionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            lightOrangeActionStyle.setBorderBottom(BorderStyle.THIN);
            lightOrangeActionStyle.setBorderTop(BorderStyle.THIN);
            lightOrangeActionStyle.setBorderRight(BorderStyle.THIN);
            lightOrangeActionStyle.setBorderLeft(BorderStyle.THIN);

            CellStyle amberActionStyle = workbook.createCellStyle();
            amberActionStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
            amberActionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            amberActionStyle.setBorderBottom(BorderStyle.THIN);
            amberActionStyle.setBorderTop(BorderStyle.THIN);
            amberActionStyle.setBorderRight(BorderStyle.THIN);
            amberActionStyle.setBorderLeft(BorderStyle.THIN);

            CellStyle greenActionStyle = workbook.createCellStyle();
            greenActionStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
            greenActionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            greenActionStyle.setBorderBottom(BorderStyle.THIN);
            greenActionStyle.setBorderTop(BorderStyle.THIN);
            greenActionStyle.setBorderRight(BorderStyle.THIN);
            greenActionStyle.setBorderLeft(BorderStyle.THIN);

            CellStyle skyBlueActionStyle = workbook.createCellStyle();
            skyBlueActionStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
            skyBlueActionStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            skyBlueActionStyle.setBorderBottom(BorderStyle.THIN);
            skyBlueActionStyle.setBorderTop(BorderStyle.THIN);
            skyBlueActionStyle.setBorderRight(BorderStyle.THIN);
            skyBlueActionStyle.setBorderLeft(BorderStyle.THIN);

            // Process messages to get alert data (reuse existing logic)
            Map<String, AlertInfoWithOwner> alertMap = new LinkedHashMap<>();

            for (MessageResponse message : messages) {
                String messageTitle = message.getMessageTitle();
                String priority = message.getPriority();
                String service = message.getService();
                boolean hasReplies = message.isHasReplies();
                int numReplies = message.getReplyCount();
                String summary = message.getMessageSummary();

                String alertName = extractAlertNameFromTitle(messageTitle);
                boolean isResolved = messageTitle.contains("[RESOLVED]");

                String serviceOwner = instanceOwnerConfig.determineOwner(alertName, summary);

                AlertInfoWithOwner info = alertMap.getOrDefault(alertName, new AlertInfoWithOwner());
                info.alertName = alertName;
                info.occurrences++;
                info.priority = info.priority == null ? priority : info.priority;
                info.service = info.service == null ? service : info.service;
                info.description = info.description == null ? summary : info.description;
                info.resolved = info.resolved || isResolved;
                info.hasReplies = info.hasReplies || hasReplies;
                info.totalReplies += numReplies;
                info.serviceOwner = serviceOwner;

                if (hasReplies) {
                    info.alertInstancesWithReplies++;
                }
                if (numReplies > info.maxRepliesOnThisAlert) {
                    info.maxRepliesOnThisAlert = numReplies;
                }

                alertMap.put(alertName, info);
            }

            // Create header row
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "Action Item", "Alert Name", "Occurrences", "Resolved_Same_Day", "Priority", "Service",
                "Has_Replies", "Number_of_Replies", "Description",
                "Alert_Types_With_Replies", "Max_Replies_On_Alert_Type", "Service Owner"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // Add data rows
            int rowNum = 1;
            for (AlertInfoWithOwner info : alertMap.values()) {
                Row row = sheet.createRow(rowNum++);

                // Determine action item based on corrected criteria
                String actionText = "";
                CellStyle actionStyle = normalStyle;

                String priority = info.priority != null ? info.priority : "Normal";
                boolean resolved = info.resolved;
                boolean hasReplies = info.hasReplies;

                // Apply the exact criteria as specified
                if ("P0".equals(priority) && !resolved && !hasReplies) {
                    // P0 + Unresolved + No replies = Orange "Actionable"
                    actionText = "Actionable";
                    actionStyle = orangeActionStyle;
                } else if ("P1".equals(priority) && !resolved && !hasReplies) {
                    // P1 + Unresolved + No replies = Light Orange "Actionable"
                    actionText = "Actionable";
                    actionStyle = lightOrangeActionStyle;
                } else if ("P0".equals(priority) && resolved && !hasReplies) {
                    // P0 + Resolved + No replies = Amber "Tune"
                    actionText = "Tune";
                    actionStyle = amberActionStyle;
                } else if ("P1".equals(priority) && resolved && !hasReplies) {
                    // P1 + Resolved + No replies = Green "Review and Tune"
                    actionText = "Review and Tune";
                    actionStyle = greenActionStyle;
                } else if ("P1".equals(priority) && resolved && hasReplies) {
                    // P1 + Resolved + Yes replies = Green "NO ACTION"
                    actionText = "NO ACTION";
                    actionStyle = greenActionStyle;
                } else if ("P0".equals(priority) && resolved && hasReplies) {
                    // P0 + Resolved + Yes replies = Light Orange "NO ACTION"
                    actionText = "NO ACTION";
                    actionStyle = lightOrangeActionStyle;
                } else if ("Normal".equals(priority) || priority == null ||
                          (!("P0".equals(priority) || "P1".equals(priority)))) {
                    // Normal priority = Sky Blue "Review"
                    actionText = "Review";
                    actionStyle = skyBlueActionStyle;
                } else {
                    // Any other P0/P1 case (with replies) = Sky Blue "Review"
                    actionText = "Review";
                    actionStyle = skyBlueActionStyle;
                }

                // Action Item column as first column (index 0)
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(actionText);
                cell0.setCellStyle(actionStyle);

                // Shift all other columns by 1
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(info.alertName);
                cell1.setCellStyle(normalStyle);

                Cell cell2 = row.createCell(2);
                cell2.setCellValue(info.occurrences);
                cell2.setCellStyle(normalStyle);

                Cell cell3 = row.createCell(3);
                cell3.setCellValue(info.resolved);
                cell3.setCellStyle(normalStyle);

                Cell cell4 = row.createCell(4);
                cell4.setCellValue(info.priority != null ? info.priority : "");
                cell4.setCellStyle(normalStyle);

                Cell cell5 = row.createCell(5);
                cell5.setCellValue(info.service != null ? info.service : "");
                cell5.setCellStyle(normalStyle);

                Cell cell6 = row.createCell(6);
                cell6.setCellValue(info.hasReplies);
                cell6.setCellStyle(normalStyle);

                Cell cell7 = row.createCell(7);
                cell7.setCellValue(info.totalReplies);
                cell7.setCellStyle(normalStyle);

                Cell cell8 = row.createCell(8);
                cell8.setCellValue(info.description != null ? info.description : "");
                cell8.setCellStyle(normalStyle);

                Cell cell9 = row.createCell(9);
                cell9.setCellValue(info.alertInstancesWithReplies);
                cell9.setCellStyle(normalStyle);

                Cell cell10 = row.createCell(10);
                cell10.setCellValue(info.maxRepliesOnThisAlert);
                cell10.setCellStyle(normalStyle);

                Cell cell11 = row.createCell(11);
                cell11.setCellValue(info.serviceOwner != null ? info.serviceOwner : "");
                cell11.setCellStyle(normalStyle);
            }

            // Auto-size columns
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // Create Legend sheet as the first sheet
            legendSheetGenerator.createLegendSheet(workbook, startDate, endDate);

            // Convert to byte array
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            return outputStream.toByteArray();

        } catch (IOException e) {
            logger.error("Error creating Excel file", e);
            throw new RuntimeException("Error creating Excel file", e);
        }
    }
}
