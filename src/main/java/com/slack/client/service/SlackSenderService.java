package com.slack.client.service;

import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.request.files.FilesGetUploadURLExternalRequest;
import com.slack.api.methods.request.files.FilesCompleteUploadExternalRequest;
import com.slack.api.methods.response.files.FilesGetUploadURLExternalResponse;
import com.slack.api.methods.response.files.FilesCompleteUploadExternalResponse;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.slack.client.model.MessageResponse;

@Service
public class SlackSenderService {

    private static final Logger logger = LoggerFactory.getLogger(SlackSenderService.class);

    @Autowired
    private MethodsClient slackClient;

    @Autowired
    private SlackService slackService;

    @Value("${slack.sender.channels}")
    private String senderChannelsConfig;

    @Value("${slack.sender.default.channelId}")
    private String defaultChannelId;

    /**
     * Send Excel alert summary to configured Slack channels
     */
    public void sendAlertSummaryToSlack(String sourceChannelId, String startDate, String endDate, int limit) {
        try {
            logger.info("Starting to send alert summary to Slack channels for source channel: {}, date range: {} to {}", 
                       sourceChannelId, startDate, endDate);

            // Generate Excel content
            byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(sourceChannelId, startDate, endDate, limit);
            
            // Get target channels
            List<String> targetChannels = getTargetChannels();
            
            // Generate filename
            String fileName = String.format("alert_summary_%s_to_%s.xlsx", startDate, endDate);
            
            // Generate message text
            String messageText = generateSlackMessage(sourceChannelId, startDate, endDate, limit);
            
            // Send to each target channel
            for (String channelId : targetChannels) {
                sendExcelToChannel(channelId, fileName, excelContent, messageText);
            }
            
            logger.info("Successfully sent alert summary to {} Slack channels", targetChannels.size());
            
        } catch (Exception e) {
            logger.error("Error sending alert summary to Slack channels", e);
            throw new RuntimeException("Error sending alert summary to Slack", e);
        }
    }

    /**
     * Send Excel file to a specific Slack channel using the new files API
     */
    private void sendExcelToChannel(String channelId, String fileName, byte[] excelContent, String messageText) {
        try {
            logger.info("Sending Excel file {} to channel {}", fileName, channelId);

            // Step 1: Get upload URL
            FilesGetUploadURLExternalRequest urlRequest = FilesGetUploadURLExternalRequest.builder()
                    .filename(fileName)
                    .length(excelContent.length)
                    .build();

            FilesGetUploadURLExternalResponse urlResponse = slackClient.filesGetUploadURLExternal(urlRequest);

            if (!urlResponse.isOk()) {
                logger.error("Failed to get upload URL for channel {}: {}", channelId, urlResponse.getError());
                throw new RuntimeException("Failed to get upload URL: " + urlResponse.getError());
            }

            String uploadUrl = urlResponse.getUploadUrl();
            String fileId = urlResponse.getFileId();

            logger.info("Got upload URL for file ID: {}", fileId);

            // Step 2: Upload file to the URL
            uploadFileToUrl(uploadUrl, fileName, excelContent);

            // Step 3: Complete the upload
            FilesCompleteUploadExternalRequest completeRequest = FilesCompleteUploadExternalRequest.builder()
                    .files(Arrays.asList(
                        FilesCompleteUploadExternalRequest.FileDetails.builder()
                            .id(fileId)
                            .title("Alert Summary Report")
                            .build()
                    ))
                    .channelId(channelId)
                    .initialComment(messageText)
                    .build();

            FilesCompleteUploadExternalResponse completeResponse = slackClient.filesCompleteUploadExternal(completeRequest);

            if (completeResponse.isOk()) {
                logger.info("Successfully uploaded Excel file to channel {}", channelId);
            } else {
                logger.error("Failed to complete upload for channel {}: {}", channelId, completeResponse.getError());
                throw new RuntimeException("Failed to complete file upload: " + completeResponse.getError());
            }

        } catch (Exception e) {
            logger.error("Error uploading Excel file to channel {}", channelId, e);
            throw new RuntimeException("Error uploading file to Slack channel", e);
        }
    }

    /**
     * Upload file content to the provided URL
     */
    private void uploadFileToUrl(String uploadUrl, String fileName, byte[] fileContent) throws IOException, InterruptedException {
        try {
            // Create a trust-all SSL context for Slack's upload URLs
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            HttpClient client = HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .build();

            // Create multipart form data
            String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
            String multipartBody = createMultipartBody(boundary, fileName, fileContent);

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(uploadUrl))
                    .header("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .POST(HttpRequest.BodyPublishers.ofString(multipartBody, StandardCharsets.UTF_8))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Failed to upload file to URL. Status: {}, Body: {}", response.statusCode(), response.body());
                throw new RuntimeException("Failed to upload file to URL. Status: " + response.statusCode());
            }

            logger.info("Successfully uploaded file to external URL");

        } catch (Exception e) {
            logger.error("Error creating SSL context or uploading file", e);
            throw new RuntimeException("Error uploading file to URL", e);
        }
    }

    /**
     * Create multipart form data for file upload
     */
    private String createMultipartBody(String boundary, String fileName, byte[] fileContent) {
        StringBuilder sb = new StringBuilder();

        // Add file part
        sb.append("--").append(boundary).append("\r\n");
        sb.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(fileName).append("\"\r\n");
        sb.append("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\r\n");
        sb.append("\r\n");

        // Convert byte array to string (this is not ideal for binary data, but works for this case)
        sb.append(new String(fileContent, StandardCharsets.ISO_8859_1));

        sb.append("\r\n--").append(boundary).append("--\r\n");

        return sb.toString();
    }

    /**
     * Generate Slack message text for the alert summary with priority statistics
     */
    private String generateSlackMessage(String sourceChannelId, String startDate, String endDate, int limit) {
        try {
            // Get messages to calculate priority statistics
            List<MessageResponse> messages = slackService.getMessagesInDateRange(sourceChannelId, startDate, endDate, limit);
            Map<String, Integer> priorityCounts = calculatePriorityCounts(messages);

            StringBuilder message = new StringBuilder();
            message.append("📊 *Alert Summary Report*\n\n");
            message.append("📅 *Date Range:* ").append(startDate).append(" to ").append(endDate).append("\n");
            message.append("📍 *Source Channel:* <#").append(sourceChannelId).append(">\n");
            message.append("🔢 *Message Limit:* ").append(limit).append("\n");
            message.append("⏰ *Generated:* ").append(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)).append("\n\n");

            // Add priority-wise alert counts
            message.append("🚨 *Priority-wise Alert Counts:*\n");
            if (priorityCounts.get("P0") > 0) {
                message.append("🔴 P0: ").append(priorityCounts.get("P0")).append(" Alerts\n");
            }
            if (priorityCounts.get("P1") > 0) {
                message.append("🟠 P1: ").append(priorityCounts.get("P1")).append(" Alerts\n");
            }
            if (priorityCounts.get("P2") > 0) {
                message.append("🟡 P2: ").append(priorityCounts.get("P2")).append(" Alerts\n");
            }
            if (priorityCounts.get("P3") > 0) {
                message.append("🟢 P3: ").append(priorityCounts.get("P3")).append(" Alerts\n");
            }
            if (priorityCounts.get("Normal") > 0) {
                message.append("⚪ Normal: ").append(priorityCounts.get("Normal")).append(" Alerts\n");
            }
            message.append("\n");

            message.append("📋 This Excel file contains a comprehensive alert summary with:\n");
            message.append("• Alert occurrences and resolution status\n");
            message.append("• Priority levels (P0, P1, P2, P3)\n");
            message.append("• Service information extracted from alert labels\n");
            message.append("• Service owner information\n");
            message.append("• Reply statistics and engagement metrics\n");
            message.append("• Color-coded action items for prioritization\n\n");
            message.append("💡 *Action Item Color Guide:*\n");
            message.append("🟠 Orange \"Actionable\" - P0/P1 Unresolved alerts needing immediate attention\n");
            message.append("🟨 Amber \"Tune\" - P0/P1 Resolved alerts that may need tuning\n");
            message.append("🔵 Blue \"Review\" - Other alerts for routine review\n");

            return message.toString();

        } catch (Exception e) {
            logger.error("Error generating Slack message with priority statistics", e);
            // Fallback to basic message without priority counts
            return generateBasicSlackMessage(sourceChannelId, startDate, endDate, limit);
        }
    }

    /**
     * Calculate priority-wise alert counts from messages
     */
    private Map<String, Integer> calculatePriorityCounts(List<MessageResponse> messages) {
        Map<String, Integer> priorityCounts = new HashMap<>();
        priorityCounts.put("P0", 0);
        priorityCounts.put("P1", 0);
        priorityCounts.put("P2", 0);
        priorityCounts.put("P3", 0);
        priorityCounts.put("Normal", 0);

        Set<String> uniqueAlerts = new HashSet<>();

        for (MessageResponse message : messages) {
            String alertName = extractAlertNameFromTitle(message.getMessageTitle());
            String priority = message.getPriority();

            // Only count unique alerts (avoid counting multiple occurrences of same alert)
            String alertKey = alertName + "_" + priority;
            if (!uniqueAlerts.contains(alertKey)) {
                uniqueAlerts.add(alertKey);

                if (priority != null) {
                    switch (priority) {
                        case "P0":
                        case "P1":
                        case "P2":
                        case "P3":
                            priorityCounts.put(priority, priorityCounts.get(priority) + 1);
                            break;
                        default:
                            priorityCounts.put("Normal", priorityCounts.get("Normal") + 1);
                            break;
                    }
                } else {
                    priorityCounts.put("Normal", priorityCounts.get("Normal") + 1);
                }
            }
        }

        return priorityCounts;
    }

    /**
     * Extract alert name from title (similar to SlackService logic)
     */
    private String extractAlertNameFromTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "Unknown Alert";
        }

        String cleanTitle = title.trim();

        // Remove any leading/trailing quotes if present
        if (cleanTitle.startsWith("\"") && cleanTitle.endsWith("\"")) {
            cleanTitle = cleanTitle.substring(1, cleanTitle.length() - 1);
        }

        // Extract the base alert name by removing only [FIRING:X] or [RESOLVED] prefixes
        Pattern firingPattern = Pattern.compile("^\\[FIRING:\\d+\\]\\s*(.+)$");
        Pattern resolvedPattern = Pattern.compile("^\\[RESOLVED\\]\\s*(.+)$");

        Matcher firingMatcher = firingPattern.matcher(cleanTitle);
        if (firingMatcher.find()) {
            return firingMatcher.group(1).trim();
        }

        Matcher resolvedMatcher = resolvedPattern.matcher(cleanTitle);
        if (resolvedMatcher.find()) {
            return resolvedMatcher.group(1).trim();
        }

        // If no pattern matches, return the clean title
        return cleanTitle;
    }

    /**
     * Generate basic Slack message without priority statistics (fallback)
     */
    private String generateBasicSlackMessage(String sourceChannelId, String startDate, String endDate, int limit) {
        StringBuilder message = new StringBuilder();
        message.append("📊 *Alert Summary Report*\n\n");
        message.append("📅 *Date Range:* ").append(startDate).append(" to ").append(endDate).append("\n");
        message.append("📍 *Source Channel:* <#").append(sourceChannelId).append(">\n");
        message.append("🔢 *Message Limit:* ").append(limit).append("\n");
        message.append("⏰ *Generated:* ").append(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)).append("\n\n");
        message.append("📋 This Excel file contains a comprehensive alert summary with:\n");
        message.append("• Alert occurrences and resolution status\n");
        message.append("• Priority levels (P0, P1, P2, P3)\n");
        message.append("• Service information extracted from alert labels\n");
        message.append("• Service owner information\n");
        message.append("• Reply statistics and engagement metrics\n");
        message.append("• Color-coded action items for prioritization\n\n");
        message.append("💡 *Action Item Color Guide:*\n");
        message.append("🟠 Orange \"Actionable\" - P0/P1 Unresolved alerts needing immediate attention\n");
        message.append("🟨 Amber \"Tune\" - P0/P1 Resolved alerts that may need tuning\n");
        message.append("🔵 Blue \"Review\" - Other alerts for routine review\n");

        return message.toString();
    }

    /**
     * Get list of target channels from configuration
     */
    private List<String> getTargetChannels() {
        if (senderChannelsConfig == null || senderChannelsConfig.trim().isEmpty()) {
            logger.warn("No sender channels configured, using default channel: {}", defaultChannelId);
            return Arrays.asList(defaultChannelId);
        }
        
        // Split by comma and trim whitespace
        List<String> channels = Arrays.asList(senderChannelsConfig.split(","));
        channels.replaceAll(String::trim);
        
        logger.info("Target channels configured: {}", channels);
        return channels;
    }

    /**
     * Send yesterday's alert summary to Slack channels
     */
    public void sendYesterdayAlertSummary() {
        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);
            
            logger.info("Sending yesterday's alert summary for date: {}", yesterdayStr);
            
            // Use default channel for source and high limit for comprehensive report
            sendAlertSummaryToSlack(defaultChannelId, yesterdayStr, yesterdayStr, 50000);
            
        } catch (Exception e) {
            logger.error("Error sending yesterday's alert summary", e);
            throw new RuntimeException("Error sending yesterday's alert summary", e);
        }
    }
}
