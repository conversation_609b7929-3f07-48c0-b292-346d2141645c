package com.slack.client.service;

import com.slack.api.methods.MethodsClient;
import com.slack.api.methods.request.files.FilesGetUploadURLExternalRequest;
import com.slack.api.methods.request.files.FilesCompleteUploadExternalRequest;
import com.slack.api.methods.response.files.FilesGetUploadURLExternalResponse;
import com.slack.api.methods.response.files.FilesCompleteUploadExternalResponse;

import java.io.IOException;
import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import com.slack.client.constants.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.slack.client.model.MessageResponse;
import com.slack.client.config.ChannelMappingConfig;

@Service
public class SlackSenderService {

    // Inner class to hold alert statistics
    private static class AlertStatistics {
        Map<String, Integer> uniqueCounts;
        Map<String, Integer> absoluteCounts;
        int totalUniqueAlerts;
        int totalAbsoluteAlerts;

        AlertStatistics() {
            uniqueCounts = new HashMap<>();
            absoluteCounts = new HashMap<>();
            uniqueCounts.put("P0", 0);
            uniqueCounts.put("P1", 0);
            uniqueCounts.put("P2", 0);
            uniqueCounts.put("P3", 0);
            uniqueCounts.put(Constants.DEFAULT_PRIORITY, 0);
            absoluteCounts.put("P0", 0);
            absoluteCounts.put("P1", 0);
            absoluteCounts.put("P2", 0);
            absoluteCounts.put("P3", 0);
            absoluteCounts.put(Constants.DEFAULT_PRIORITY, 0);
            totalUniqueAlerts = 0;
            totalAbsoluteAlerts = 0;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(SlackSenderService.class);

    @Autowired
    private MethodsClient slackClient;

    @Autowired
    private SlackService slackService;

    @Autowired
    private ChannelMappingConfig channelMappingConfig;

    @Value("${slack.sender.channels}")
    private String senderChannelsConfig;

    @Value("${slack.sender.default.channelId}")
    private String defaultChannelId;

    /**
     * Send Excel alert summary to configured Slack channels
     */
    public void sendAlertSummaryToSlack(String sourceChannelId, String startDate, String endDate, int limit) {
        try {
            logger.info("Starting to send alert summary to Slack channels for source channel: {}, date range: {} to {}", 
                       sourceChannelId, startDate, endDate);

            // Generate Excel content
            byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(sourceChannelId, startDate, endDate, limit);
            
            // Get target channels
            List<String> targetChannels = getTargetChannels();
            
            // Generate filename
            String fileName = String.format("alert_summary_%s_to_%s.xlsx", startDate, endDate);
            
            // Generate message text
            String messageText = generateSlackMessage(sourceChannelId, startDate, endDate, limit);
            
            // Send to each target channel
            for (String channelId : targetChannels) {
                sendExcelToChannel(channelId, fileName, excelContent, messageText);
            }
            
            logger.info("Successfully sent alert summary to {} Slack channels", targetChannels.size());
            
        } catch (Exception e) {
            logger.error("Error sending alert summary to Slack channels", e);
            throw new RuntimeException("Error sending alert summary to Slack", e);
        }
    }

    /**
     * Send Excel file to a specific Slack channel using the new files API
     */
    private void sendExcelToChannel(String channelId, String fileName, byte[] excelContent, String messageText) {
        try {
            logger.info("Sending Excel file {} to channel {}", fileName, channelId);

            // Step 1: Get upload URL
            FilesGetUploadURLExternalRequest urlRequest = FilesGetUploadURLExternalRequest.builder()
                    .filename(fileName)
                    .length(excelContent.length)
                    .build();

            FilesGetUploadURLExternalResponse urlResponse = slackClient.filesGetUploadURLExternal(urlRequest);

            if (!urlResponse.isOk()) {
                logger.error("Failed to get upload URL for channel {}: {}", channelId, urlResponse.getError());
                throw new RuntimeException("Failed to get upload URL: " + urlResponse.getError());
            }

            String uploadUrl = urlResponse.getUploadUrl();
            String fileId = urlResponse.getFileId();

            logger.info("Got upload URL for file ID: {}", fileId);

            // Step 2: Upload file to the URL
            uploadFileToUrl(uploadUrl, fileName, excelContent);

            // Step 3: Complete the upload
            FilesCompleteUploadExternalRequest completeRequest = FilesCompleteUploadExternalRequest.builder()
                    .files(Arrays.asList(
                        FilesCompleteUploadExternalRequest.FileDetails.builder()
                            .id(fileId)
                            .title("Alert Summary Report")
                            .build()
                    ))
                    .channelId(channelId)
                    .initialComment(messageText)
                    .build();

            FilesCompleteUploadExternalResponse completeResponse = slackClient.filesCompleteUploadExternal(completeRequest);

            if (completeResponse.isOk()) {
                logger.info("Successfully uploaded Excel file to channel {}", channelId);
            } else {
                logger.error("Failed to complete upload for channel {}: {}", channelId, completeResponse.getError());
                throw new RuntimeException("Failed to complete file upload: " + completeResponse.getError());
            }

        } catch (Exception e) {
            logger.error("Error uploading Excel file to channel {}", channelId, e);
            throw new RuntimeException("Error uploading file to Slack channel", e);
        }
    }

    /**
     * Upload file content to the provided URL using proper binary handling
     */
    private void uploadFileToUrl(String uploadUrl, String fileName, byte[] fileContent) throws IOException, InterruptedException {
        try {
            // Create a trust-all SSL context for Slack's upload URLs
            TrustManager[] trustAllCerts = new TrustManager[] {
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() { return null; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
            };

            SSLContext sslContext = SSLContext.getInstance("TLS");
            sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

            HttpClient client = HttpClient.newBuilder()
                    .sslContext(sslContext)
                    .build();

            // Create multipart form data with proper binary handling
            String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();
            byte[] multipartBody = createMultipartBodyBytes(boundary, fileName, fileContent);

            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(uploadUrl))
                    .header("Content-Type", "multipart/form-data; boundary=" + boundary)
                    .POST(HttpRequest.BodyPublishers.ofByteArray(multipartBody))
                    .build();

            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() != 200) {
                logger.error("Failed to upload file to URL. Status: {}, Body: {}", response.statusCode(), response.body());
                throw new RuntimeException("Failed to upload file to URL. Status: " + response.statusCode());
            }

            logger.info("Successfully uploaded file to external URL");

        } catch (Exception e) {
            logger.error("Error creating SSL context or uploading file", e);
            throw new RuntimeException("Error uploading file to URL", e);
        }
    }

    /**
     * Create multipart form data for file upload with proper binary handling
     */
    private byte[] createMultipartBodyBytes(String boundary, String fileName, byte[] fileContent) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // Add file part header
        String header = "--" + boundary + "\r\n" +
                       "Content-Disposition: form-data; name=\"file\"; filename=\"" + fileName + "\"\r\n" +
                       "Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\r\n" +
                       "\r\n";

        outputStream.write(header.getBytes(StandardCharsets.UTF_8));

        // Add binary file content directly
        outputStream.write(fileContent);

        // Add closing boundary
        String footer = "\r\n--" + boundary + "--\r\n";
        outputStream.write(footer.getBytes(StandardCharsets.UTF_8));

        return outputStream.toByteArray();
    }

    /**
     * Generate Slack message text for the alert summary with priority statistics
     */
    private String generateSlackMessage(String sourceChannelId, String startDate, String endDate, int limit) {
        try {
            // Get messages to calculate priority statistics
            List<MessageResponse> messages = slackService.getMessagesInDateRange(sourceChannelId, startDate, endDate, limit);
            AlertStatistics stats = calculateAlertStatistics(messages);

            StringBuilder message = new StringBuilder();
            message.append("📊 *Alert Summary Report*\n\n");
            message.append("📅 *Date Range:* ").append(startDate).append(" to ").append(endDate).append("\n");
            message.append("📍 *Source Channel:* <#").append(sourceChannelId).append(">\n");
            message.append("🔢 *Message Limit:* ").append(limit).append("\n");
            message.append("⏰ *Generated:* ").append(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)).append("\n\n");

            // Add priority-wise unique alert counts
            message.append("🚨 *Priority-wise Unique Alert Counts:*\n");
            if (stats.uniqueCounts.get("P0") > 0) {
                message.append("🔴 P0: ").append(stats.uniqueCounts.get("P0")).append(" Alerts\n");
            }
            if (stats.uniqueCounts.get("P1") > 0) {
                message.append("🟠 P1: ").append(stats.uniqueCounts.get("P1")).append(" Alerts\n");
            }
            if (stats.uniqueCounts.get("P2") > 0) {
                message.append("🟡 P2: ").append(stats.uniqueCounts.get("P2")).append(" Alerts\n");
            }
            if (stats.uniqueCounts.get("P3") > 0) {
                message.append("🟢 P3: ").append(stats.uniqueCounts.get("P3")).append(" Alerts\n");
            }
            if (stats.uniqueCounts.get(Constants.DEFAULT_PRIORITY) > 0) {
                message.append("⚪ " + Constants.DEFAULT_PRIORITY+ ": ").append(stats.uniqueCounts.get(Constants.DEFAULT_PRIORITY)).append(" Alerts\n");
            }
            message.append("📈 *Total Unique Alert Count:* ").append(stats.totalUniqueAlerts).append("\n\n");

            // Add priority-wise absolute alert counts
            message.append("📊 *Priority-wise Absolute Alert Counts:*\n");
            if (stats.absoluteCounts.get("P0") > 0) {
                message.append("🔴 P0: ").append(stats.absoluteCounts.get("P0")).append(" Alerts\n");
            }
            if (stats.absoluteCounts.get("P1") > 0) {
                message.append("🟠 P1: ").append(stats.absoluteCounts.get("P1")).append(" Alerts\n");
            }
            if (stats.absoluteCounts.get("P2") > 0) {
                message.append("🟡 P2: ").append(stats.absoluteCounts.get("P2")).append(" Alerts\n");
            }
            if (stats.absoluteCounts.get("P3") > 0) {
                message.append("🟢 P3: ").append(stats.absoluteCounts.get("P3")).append(" Alerts\n");
            }
            if (stats.absoluteCounts.get(Constants.DEFAULT_PRIORITY) > 0) {
                message.append("⚪ " + Constants.DEFAULT_PRIORITY + ": ").append(stats.absoluteCounts.get(Constants.DEFAULT_PRIORITY)).append(" Alerts\n");
            }
            message.append("📊 *Total Absolute Alert Count:* ").append(stats.totalAbsoluteAlerts).append("\n\n");

//            message.append("📋 This Excel file contains a comprehensive alert summary with:\n");
//            message.append("• Alert occurrences and resolution status\n");
//            message.append("• Priority levels (P0, P1, P2)\n");
//            message.append("• Service information extracted from alert labels\n");
//            message.append("• Service owner information\n");
//            message.append("• Reply statistics and engagement metrics\n");
//            message.append("• Color-coded action items for prioritization\n\n");
//            message.append("💡 *Action Item Color Guide:*\n");
//            message.append("🟠 Orange \"Actionable\" - P0/P1 Unresolved alerts needing immediate attention\n");
//            message.append("🟨 Amber \"Tune\" - P0/P1 Resolved alerts that may need tuning\n");
//            message.append("🔵 Blue \"Review\" - Other alerts for routine review\n");

            return message.toString();

        } catch (Exception e) {
            logger.error("Error generating Slack message with priority statistics", e);
            // Fallback to basic message without priority counts
            return generateBasicSlackMessage(sourceChannelId, startDate, endDate, limit);
        }
    }

    /**
     * Calculate both unique and absolute alert statistics from messages
     */
    private AlertStatistics calculateAlertStatistics(List<MessageResponse> messages) {
        AlertStatistics stats = new AlertStatistics();
        Set<String> uniqueAlerts = new HashSet<>();

        for (MessageResponse message : messages) {
            String alertName = extractAlertNameFromTitle(message.getMessageTitle());
            String priority = message.getPriority();

            // Normalize priority
            String normalizedPriority = normalizePriority(priority);

            // Count absolute alerts (all messages)
            stats.absoluteCounts.put(normalizedPriority, stats.absoluteCounts.get(normalizedPriority) + 1);
            stats.totalAbsoluteAlerts++;

            // Count unique alerts (avoid counting multiple occurrences of same alert)
            String alertKey = alertName + "_" + normalizedPriority;
            if (!uniqueAlerts.contains(alertKey)) {
                uniqueAlerts.add(alertKey);
                stats.uniqueCounts.put(normalizedPriority, stats.uniqueCounts.get(normalizedPriority) + 1);
                stats.totalUniqueAlerts++;
            }
        }

        return stats;
    }

    /**
     * Normalize priority values to standard format
     */
    private String normalizePriority(String priority) {
        if (priority != null) {
            switch (priority.toUpperCase()) {
                case "P0":
                case "P1":
                case "P2":
                case "P3":
                    return priority.toUpperCase();
                default:
                    return Constants.DEFAULT_PRIORITY;
            }
        }
        return Constants.DEFAULT_PRIORITY;
    }

    /**
     * Extract alert name from title (similar to SlackService logic)
     */
    private String extractAlertNameFromTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            return "Unknown Alert";
        }

        String cleanTitle = title.trim();

        // Remove any leading/trailing quotes if present
        if (cleanTitle.startsWith("\"") && cleanTitle.endsWith("\"")) {
            cleanTitle = cleanTitle.substring(1, cleanTitle.length() - 1);
        }

        // Extract the base alert name by removing only [FIRING:X] or [RESOLVED] prefixes
        Pattern firingPattern = Pattern.compile("^\\[FIRING:\\d+\\]\\s*(.+)$");
        Pattern resolvedPattern = Pattern.compile("^\\[RESOLVED\\]\\s*(.+)$");

        Matcher firingMatcher = firingPattern.matcher(cleanTitle);
        if (firingMatcher.find()) {
            return firingMatcher.group(1).trim();
        }

        Matcher resolvedMatcher = resolvedPattern.matcher(cleanTitle);
        if (resolvedMatcher.find()) {
            return resolvedMatcher.group(1).trim();
        }

        // If no pattern matches, return the clean title
        return cleanTitle;
    }

    /**
     * Generate basic Slack message without priority statistics (fallback)
     */
    private String generateBasicSlackMessage(String sourceChannelId, String startDate, String endDate, int limit) {
        StringBuilder message = new StringBuilder();
        message.append("📊 *Alert Summary Report*\n\n");
        message.append("📅 *Date Range:* ").append(startDate).append(" to ").append(endDate).append("\n");
        message.append("📍 *Source Channel:* <#").append(sourceChannelId).append(">\n");
        message.append("🔢 *Message Limit:* ").append(limit).append("\n");
        message.append("⏰ *Generated:* ").append(LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE)).append("\n\n");
        message.append("📋 This Excel file contains a comprehensive alert summary with:\n");
        message.append("• Alert occurrences and resolution status\n");
        message.append("• Priority levels (P0, P1, P2, P3)\n");
        message.append("• Service information extracted from alert labels\n");
        message.append("• Service owner information\n");
        message.append("• Reply statistics and engagement metrics\n");
        message.append("• Color-coded action items for prioritization\n\n");
        message.append("💡 *Action Item Color Guide:*\n");
        message.append("🟠 Orange \"Actionable\" - P0/P1 Unresolved alerts needing immediate attention\n");
        message.append("🟨 Amber \"Tune\" - P0/P1 Resolved alerts that may need tuning\n");
        message.append("🔵 Blue \"Review\" - Other alerts for routine review\n");

        return message.toString();
    }

    /**
     * Get list of target channels from configuration
     */
    private List<String> getTargetChannels() {
        if (senderChannelsConfig == null || senderChannelsConfig.trim().isEmpty()) {
            logger.warn("No sender channels configured, using default channel: {}", defaultChannelId);
            return Arrays.asList(defaultChannelId);
        }
        
        // Split by comma and trim whitespace
        List<String> channels = Arrays.asList(senderChannelsConfig.split(","));
        channels.replaceAll(String::trim);
        
        logger.info("Target channels configured: {}", channels);
        return channels;
    }

    /**
     * Send yesterday's alert summary to Slack channels (Legacy method)
     */
    public void sendYesterdayAlertSummary() {
        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

            logger.info("Sending yesterday's alert summary for date: {}", yesterdayStr);

            // Use default channel for source and high limit for comprehensive report
            sendAlertSummaryToSlack(defaultChannelId, yesterdayStr, yesterdayStr, Constants.DEFAULT_MESSAGE_FETCH_LIMIT);

        } catch (Exception e) {
            logger.error("Error sending yesterday's alert summary", e);
            throw new RuntimeException("Error sending yesterday's alert summary", e);
        }
    }

    /**
     * Send alert summary to Slack channels based on source-to-target configuration mappings
     */
    public void sendAlertSummaryBasedOnMappings(String startDate, String endDate, int limit) {
        try {
            logger.info("Starting to send alert summary based on source-to-target mappings for date range: {} to {}",
                       startDate, endDate);

            List<ChannelMappingConfig.SourceToTargetMapping> slackMappings = channelMappingConfig.getSlackMappings();

            if (slackMappings.isEmpty()) {
                logger.warn("No Slack source-to-target mappings configured. Falling back to legacy method.");
                sendYesterdayAlertSummary();
                return;
            }

            int totalChannelsSent = 0;

            for (ChannelMappingConfig.SourceToTargetMapping mapping : slackMappings) {
                String sourceChannel = mapping.getSource();
                List<String> targetChannels = mapping.getTargets();

                logger.info("Processing mapping: source={}, targets={}", sourceChannel, targetChannels);

                try {
                    // Generate Excel content for this source channel
                    byte[] excelContent = slackService.generateAlertSummaryWithOwnerExcel(sourceChannel, startDate, endDate, limit);

                    // Generate filename
                    String fileName = String.format("alert_summary_%s_to_%s.xlsx", startDate, endDate);

                    // Generate message text
                    String messageText = generateSlackMessage(sourceChannel, startDate, endDate, limit);

                    // Send to each target channel for this source
                    for (String targetChannel : targetChannels) {
                        sendExcelToChannel(targetChannel, fileName, excelContent, messageText);
                        totalChannelsSent++;
                    }

                    logger.info("Successfully sent alert summary from source {} to {} target channels",
                               sourceChannel, targetChannels.size());

                } catch (Exception e) {
                    logger.error("Error processing mapping for source channel {}: {}", sourceChannel, e.getMessage(), e);
                    // Continue with other mappings even if one fails
                }
            }

            logger.info("Successfully sent alert summary to {} total Slack channels across {} source mappings",
                       totalChannelsSent, slackMappings.size());

        } catch (Exception e) {
            logger.error("Error sending alert summary based on mappings", e);
            throw new RuntimeException("Error sending alert summary based on mappings", e);
        }
    }

    /**
     * Send yesterday's alert summary based on source-to-target configuration mappings
     */
    public void sendYesterdayAlertSummaryBasedOnMappings() {
        try {
            LocalDate yesterday = LocalDate.now().minusDays(1);
            String yesterdayStr = yesterday.format(DateTimeFormatter.ISO_LOCAL_DATE);

            logger.info("Sending yesterday's alert summary based on mappings for date: {}", yesterdayStr);

            sendAlertSummaryBasedOnMappings(yesterdayStr, yesterdayStr, Constants.DEFAULT_MESSAGE_FETCH_LIMIT);

        } catch (Exception e) {
            logger.error("Error sending yesterday's alert summary based on mappings", e);
            throw new RuntimeException("Error sending yesterday's alert summary based on mappings", e);
        }
    }
}
