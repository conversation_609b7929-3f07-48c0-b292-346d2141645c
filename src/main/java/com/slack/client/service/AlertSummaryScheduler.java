package com.slack.client.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

@Service
public class AlertSummaryScheduler {

    private static final Logger logger = LoggerFactory.getLogger(AlertSummaryScheduler.class);

    @Autowired
    private EmailService emailService;

    /**
     * Scheduled method to send daily alert summary email at 11:30 AM every day
     * Cron expression: "0 30 11 * * ?" means:
     * - 0 seconds
     * - 30 minutes
     * - 11 hours (11 AM)
     * - every day of month
     * - every month
     * - every day of week
     */
    //@Scheduled(cron = "0 30 11 * * ?")
    public void sendDailyAlertSummary() {
        logger.info("Starting scheduled daily alert summary email task");

        try {
            // Use new configuration-based method
            emailService.sendYesterdayAlertSummaryBasedOnMappings();
            logger.info("Daily alert summary email task completed successfully");
        } catch (Exception e) {
            logger.error("Error in scheduled daily alert summary email task: {}", e.getMessage(), e);
        }
    }
}
