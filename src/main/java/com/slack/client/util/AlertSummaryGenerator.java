package com.slack.client.util;

import com.opencsv.*;
import com.opencsv.exceptions.CsvValidationException;

import java.io.*;
import java.util.*;
import java.util.regex.*;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class AlertSummaryGenerator {

    private static final Logger logger = LoggerFactory.getLogger(AlertSummaryGenerator.class);

    static class AlertInfo {
        String alertName;
        String priority;
        String description;
        int occurrences = 0;
        boolean resolved = false;
        boolean hasReplies = false;
        int totalReplies = 0;
    }

    public static void main(String[] args) throws IOException, CsvValidationException {
        String inputPath = "/Users/<USER>/Work/ai/logs_and_data/messages_5jun-main.csv";
        String outputPath = "/Users/<USER>/Work/ai/logs_and_data/alert_summary_detailed_java.csv";

        generateAlertSummary(inputPath, outputPath);

    }

    private static void generateAlertSummary(String inputPath, String outputPath) throws IOException, CsvValidationException {
        Map<String, AlertInfo> alertMap = new LinkedHashMap<>();

        try (CSVReader reader = new CSVReader(new FileReader(inputPath))) {
            String[] headers = reader.readNext();
            Map<String, Integer> headerMap = getHeaderIndex(headers);

            String[] row;
            while ((row = reader.readNext()) != null) {
                String messageTitle = row[headerMap.get("Message Title")];
                String priority = row[headerMap.get("Priority")];
                String hasRepliesStr = row[headerMap.get("Has Replies")];
                String repliesStr = row[headerMap.get("Number of Replies")];
                String summary = row[headerMap.get("Message Summary")];

                String alertName = extractAlertName(messageTitle);
                boolean isResolved = messageTitle.contains("[RESOLVED]");
                boolean hasReplies = hasRepliesStr.equalsIgnoreCase("true");
                int numReplies = Integer.parseInt(repliesStr);

                AlertInfo info = alertMap.getOrDefault(alertName, new AlertInfo());
                info.alertName = alertName;
                info.occurrences++;
                info.priority = info.priority == null ? priority : info.priority;
                info.description = info.description == null ? summary : info.description;
                info.resolved = info.resolved || isResolved;
                info.hasReplies = info.hasReplies || hasReplies;
                info.totalReplies += numReplies;

                alertMap.put(alertName, info);
            }
        }

        try (CSVWriter writer = new CSVWriter(new FileWriter(outputPath))) {
            String[] header = {
                    "Alert Name", "Occurrences", "Resolved_Same_Day", "Priority",
                    "Has_Replies", "Number_of_Replies", "Description",
                    "Alert_Types_With_Replies", "Max_Replies_On_Alert_Type"
            };
            writer.writeNext(header);

            for (AlertInfo info : alertMap.values()) {
                String[] line = {
                        info.alertName,
                        String.valueOf(info.occurrences),
                        String.valueOf(info.resolved),
                        info.priority,
                        String.valueOf(info.hasReplies),
                        String.valueOf(info.totalReplies),
                        info.description,
                        String.valueOf(info.hasReplies ? 1 : 0),
                        String.valueOf(info.totalReplies)
                };
                writer.writeNext(line);
            }
        }

        logger.info("Summary written to: {}", outputPath);
    }

    private static String extractAlertName(String title) {
        Matcher matcher = Pattern.compile("(?:FIRING:\\d+\\] |\\[RESOLVED\\] )(.+)$").matcher(title);
        return matcher.find() ? matcher.group(1).trim() : title.trim();
    }

    private static Map<String, Integer> getHeaderIndex(String[] headers) {
        Map<String, Integer> map = new HashMap<>();
        for (int i = 0; i < headers.length; i++) {
            map.put(headers[i].trim(), i);
        }
        return map;
    }
}


