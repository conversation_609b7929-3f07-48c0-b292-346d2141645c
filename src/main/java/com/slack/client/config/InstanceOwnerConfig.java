package com.slack.client.config;

import org.springframework.stereotype.Component;

import java.util.*;

@Component
public class InstanceOwnerConfig {

    private final Map<String, OwnerInfo> instanceOwners;
    private final OwnerInfo defaultOwner;

    public InstanceOwnerConfig() {
        this.instanceOwners = initializeInstanceOwners();
        this.defaultOwner = new OwnerInfo("no-owner", "No Owner", "Instances not assigned to any team", new ArrayList<>());
    }

    private Map<String, OwnerInfo> initializeInstanceOwners() {
        Map<String, OwnerInfo> owners = new HashMap<>();

        // Team Harish
        owners.put("team_harish", new OwnerInfo(
            "team_harish",
            "Team Harish",
            "Team Harish instances",
            Arrays.asList("bff", "ffr", "saga", "fs_recharge", "catalog", "renewal", "billsync", 
                         "rechargeskafka", "rechargesredis", "bills-sync", "subscription", "automatic", 
                         "txnsqueue", "consent", "rechargeadmin", "iocl", "eslogstash",
                         "rechargelogstash", "express", "healthcheck", "fsrecharge", "searchstatselasticsearch", 
                         "rechargesprodkafka", "mirrorkafka", "merchantcallback", "policy", "commonmysql", 
                         "rechargezookeeper", "stateredissentinel", "orderclear", "dcat", "ruleengine", 
                         "bpap", "deferred", "s2sagents", "dact", "inreconnode", "sagaconsumer-reminderspringboot", 
                         "smartreminder")
        ));

        // Team Arvind
        owners.put("team_arvind", new OwnerInfo(
            "team_arvind",
            "Team Arvind",
            "Team Arvind instances",
            Arrays.asList("reminder", "notification", "drop_off", "data-retriever", "sms", "whatsapp", 
                         "analytics", "heuristic", "nonrureminder", "notifi", "nonru", "rule-processor", 
                         "ruhomepagecassandra", "publisher", "integrationkafka", "bills", "auxiliarykafka", 
                         "recentbills", "ebps", "dropoff")
        ));

        // Team Priya
        owners.put("team_priya", new OwnerInfo(
            "team_priya",
            "Team Priya",
            "Team Priya instances",
            Arrays.asList("euronet", "rechargeplan", "upms", "mnpconsumer", "settlement", "airtel", "bfsi", "biller",
                         "rent", "utilgw", "viprepaid", "vodafone", "mnp", "gateway", "billdesk", "jio", 
                         "digitaledugwnode", "digitalcatalog-rechargeplan", "digitalcatalog-gvtn-rechargeplan")
        ));

        // Team Pankaj
        owners.put("team_pankaj", new OwnerInfo(
            "team_pankaj",
            "Team Pankaj",
            "Team Pankaj instances",
            Arrays.asList("devop", "devops", "infra", "jenkins", "prometheus", "spinnaker", 
                         "central-logstashlogstash", "elk", "nginx", "tomcat", "inventory", "sftp", 
                         "maxwell", "ubuntu", "ami")
        ));

        // Team Sanjay
        owners.put("team_sanjay", new OwnerInfo(
            "team_sanjay",
            "Team Sanjay",
            "Team Sanjay instances",
            Arrays.asList("mock", "rechdockercontainer-qa-slave")
        ));

        // Team Vinit
        owners.put("team_vinit", new OwnerInfo(
            "team_vinit",
            "Team Vinit",
            "Team Vinit instances",
            Arrays.asList("abhimanyu")
        ));

        return owners;
    }

    public String determineOwner(String alertName, String description) {
        String searchText = (alertName + " " + (description != null ? description : "")).toLowerCase();
        
        for (OwnerInfo owner : instanceOwners.values()) {
            for (String substring : owner.getSubstrings()) {
                if (searchText.contains(substring.toLowerCase())) {
                    return owner.getName();
                }
            }
        }
        
        return defaultOwner.getName();
    }

    public static class OwnerInfo {
        private final String id;
        private final String name;
        private final String description;
        private final List<String> substrings;

        public OwnerInfo(String id, String name, String description, List<String> substrings) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.substrings = substrings;
        }

        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public List<String> getSubstrings() { return substrings; }
    }
}
