package com.slack.client.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
public class ChannelMappingConfig {

    private static final Logger logger = LoggerFactory.getLogger(ChannelMappingConfig.class);

    @Value("${email.sender.sourceToTarget.channels.mapping:[]}")
    private String emailMappingConfig;

    @Value("${slack.sender.sourceToTarget.channels.mapping:[]}")
    private String slackMappingConfig;

    private List<SourceToTargetMapping> emailMappings = new ArrayList<>();
    private List<SourceToTargetMapping> slackMappings = new ArrayList<>();

    private final ObjectMapper objectMapper = new ObjectMapper();

    @PostConstruct
    public void init() {
        parseEmailMappings();
        parseSlackMappings();
    }

    private void parseEmailMappings() {
        try {
            if (emailMappingConfig != null && !emailMappingConfig.trim().isEmpty() && !emailMappingConfig.equals("[]")) {
                TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
                List<Map<String, Object>> mappingList = objectMapper.readValue(emailMappingConfig, typeRef);
                
                for (Map<String, Object> mapping : mappingList) {
                    String source = (String) mapping.get("source");
                    @SuppressWarnings("unchecked")
                    List<String> targets = (List<String>) mapping.get("target");
                    
                    if (source != null && targets != null && !targets.isEmpty()) {
                        emailMappings.add(new SourceToTargetMapping(source, targets));
                    }
                }
                
                logger.info("Loaded {} email source-to-target mappings", emailMappings.size());
            } else {
                logger.info("No email source-to-target mappings configured");
            }
        } catch (Exception e) {
            logger.error("Error parsing email source-to-target mappings: {}", e.getMessage(), e);
            emailMappings = new ArrayList<>();
        }
    }

    private void parseSlackMappings() {
        try {
            if (slackMappingConfig != null && !slackMappingConfig.trim().isEmpty() && !slackMappingConfig.equals("[]")) {
                TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
                List<Map<String, Object>> mappingList = objectMapper.readValue(slackMappingConfig, typeRef);
                
                for (Map<String, Object> mapping : mappingList) {
                    String source = (String) mapping.get("source");
                    @SuppressWarnings("unchecked")
                    List<String> targets = (List<String>) mapping.get("target");
                    
                    if (source != null && targets != null && !targets.isEmpty()) {
                        slackMappings.add(new SourceToTargetMapping(source, targets));
                    }
                }
                
                logger.info("Loaded {} slack source-to-target mappings", slackMappings.size());
            } else {
                logger.info("No slack source-to-target mappings configured");
            }
        } catch (Exception e) {
            logger.error("Error parsing slack source-to-target mappings: {}", e.getMessage(), e);
            slackMappings = new ArrayList<>();
        }
    }

    public List<SourceToTargetMapping> getEmailMappings() {
        return emailMappings;
    }

    public List<SourceToTargetMapping> getSlackMappings() {
        return slackMappings;
    }

    public List<String> getEmailTargetsForSource(String sourceChannel) {
        return emailMappings.stream()
                .filter(mapping -> mapping.getSource().equals(sourceChannel))
                .findFirst()
                .map(SourceToTargetMapping::getTargets)
                .orElse(new ArrayList<>());
    }

    public List<String> getSlackTargetsForSource(String sourceChannel) {
        return slackMappings.stream()
                .filter(mapping -> mapping.getSource().equals(sourceChannel))
                .findFirst()
                .map(SourceToTargetMapping::getTargets)
                .orElse(new ArrayList<>());
    }

    // Inner class to represent source-to-target mapping
    public static class SourceToTargetMapping {
        private String source;
        private List<String> targets;

        public SourceToTargetMapping() {}

        public SourceToTargetMapping(String source, List<String> targets) {
            this.source = source;
            this.targets = targets;
        }

        public String getSource() {
            return source;
        }

        public void setSource(String source) {
            this.source = source;
        }

        public List<String> getTargets() {
            return targets;
        }

        public void setTargets(List<String> targets) {
            this.targets = targets;
        }

        @Override
        public String toString() {
            return "SourceToTargetMapping{" +
                    "source='" + source + '\'' +
                    ", targets=" + targets +
                    '}';
        }
    }
}
