# Server Configuration
server.port=8080
server.servlet.context-path=/

# Slack Configuration
slack.bot.token=******************************************************
slack.channel.id=G01EGLYRE7L

# Logging Configuration is now handled by log4j2.xml

# Application Configuration
spring.application.name=slack-client-api

# Email Configuration
peppipostApi.url=https://gptmtrans.pepipost.com/v5/mail/send
peppipostApi.key=********************************

# Alert Summary Mail Config
alertSummary.mail.from.email=<EMAIL>
alertSummary.mail.from.name=BBPS Dev
alertSummary.mail.replyTo=<EMAIL>
alertSummary.mail.to={'<EMAIL>':'<PERSON>ura<PERSON><PERSON>'}
alertSummary.mail.channelId=C092JU4SM9R

# Slack Sender Configuration (Legacy - kept for backward compatibility)
# comma separated list of channels to send alert summary to e.g. C092JU4SM9R,C0919KSNTJ5 : travel-channels: C0936DVTEBB
slack.sender.channels=C092JU4SM9R,C0919KSNTJ5
# default channelId will be used by slack sender service to fetch messages from
slack.sender.default.channelId=C018GQEHDFV

# New Source-to-Target Channel Mappings
# Email Sender Configuration - JSON array of source to target mappings
email.sender.sourceToTarget.channels.mapping=[{"source":"C018GQEHDFV", "target":["<EMAIL>"]},{"source":"G01EGLYRE7L", "target":["<EMAIL>"]}]

# Slack Sender Configuration - JSON array of source to target mappings
slack.sender.sourceToTarget.channels.mapping=[{"source":"C018GQEHDFV", "target":["C092JU4SM9R","C0919KSNTJ5"]},{"source":"G01EGLYRE7L", "target":["G01CWKY1C7J"]},{"source":"C05T8EBHWHK", "target":["C0936DVTEBB"]}]
