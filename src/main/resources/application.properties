# Server Configuration
server.port=8081
server.servlet.context-path=/

# Slack Configuration
slack.bot.token=******************************************************
slack.channel.id=G01EGLYRE7L

# Logging Configuration
logging.level.com.slack.client=DEBUG
logging.level.com.slack.api=INFO
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Application Configuration
spring.application.name=slack-client-api

# Email Configuration
peppipostApi.url=https://gptmtrans.pepipost.com/v5/mail/send
peppipostApi.key=********************************

# Alert Summary Mail Config
alertSummary.mail.from.email=<EMAIL>
alertSummary.mail.from.name=BBPS Dev
alertSummary.mail.replyTo=<EMAIL>
alertSummary.mail.to={'<EMAIL>':'<PERSON>ura<PERSON><PERSON>'}
alertSummary.mail.channelId=C018GQEHDFV

# Slack Sender Configuration
slack.sender.channels=C092JU4SM9R
slack.sender.default.channelId=C092JU4SM9R
