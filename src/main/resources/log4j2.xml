<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <!-- Log file path -->
        <Property name="LOG_PATH">/Users/<USER>/Work/logs/ru_tools_java</Property>
        
        <!-- Log pattern for console -->
        <Property name="CONSOLE_PATTERN">%d{yyyy-MM-dd HH:mm:ss} [%level] %logger{36} - %msg%n</Property>
        
        <!-- Log pattern for file -->
        <Property name="FILE_PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%thread] %logger{36} - %msg%n</Property>
        
        <!-- Application name -->
        <Property name="APP_NAME">ru_tools_java</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- Application Log File Appender -->
        <RollingFile name="ApplicationFile" fileName="${LOG_PATH}/${APP_NAME}.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="100MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- Error Log File Appender -->
        <RollingFile name="ErrorFile" fileName="${LOG_PATH}/${APP_NAME}-error.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-error-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="30"/>
            <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- Slack API Log File Appender -->
        <RollingFile name="SlackApiFile" fileName="${LOG_PATH}/${APP_NAME}-slack-api.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-slack-api-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- Email Service Log File Appender -->
        <RollingFile name="EmailServiceFile" fileName="${LOG_PATH}/${APP_NAME}-email.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-email-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>

        <!-- Scheduler Log File Appender -->
        <RollingFile name="SchedulerFile" fileName="${LOG_PATH}/${APP_NAME}-scheduler.log"
                     filePattern="${LOG_PATH}/${APP_NAME}-scheduler-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="${FILE_PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="50MB"/>
            </Policies>
            <DefaultRolloverStrategy max="15"/>
            <ThresholdFilter level="DEBUG" onMatch="ACCEPT" onMismatch="DENY"/>
        </RollingFile>
    </Appenders>

    <Loggers>
        <!-- Application specific loggers -->
        <Logger name="com.slack.client" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ApplicationFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Slack API specific logger -->
        <Logger name="com.slack.api" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="SlackApiFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Email service specific logger -->
        <Logger name="com.slack.client.service.EmailService" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="EmailServiceFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Scheduler specific loggers -->
        <Logger name="com.slack.client.service.SlackSenderScheduler" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="SchedulerFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <Logger name="com.slack.client.service.AlertSummaryScheduler" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="SchedulerFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Logger>

        <!-- Spring Boot loggers -->
        <Logger name="org.springframework.boot" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ApplicationFile"/>
        </Logger>

        <Logger name="org.springframework.web" level="INFO" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ApplicationFile"/>
        </Logger>

        <!-- HTTP Client loggers -->
        <Logger name="org.apache.http" level="WARN" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ApplicationFile"/>
        </Logger>

        <!-- Root logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="ApplicationFile"/>
            <AppenderRef ref="ErrorFile"/>
        </Root>
    </Loggers>
</Configuration>
