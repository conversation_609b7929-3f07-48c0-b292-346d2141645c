<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Properties>
        <!-- Use target directory for test logs -->
        <Property name="LOG_PATH">target/test-logs</Property>
        
        <!-- Simple console pattern for tests -->
        <Property name="CONSOLE_PATTERN">%d{HH:mm:ss.SSS} [%level] %logger{36} - %msg%n</Property>
        
        <!-- Application name -->
        <Property name="APP_NAME">ru_tools_java_test</Property>
    </Properties>

    <Appenders>
        <!-- Console Appender for tests -->
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
            <ThresholdFilter level="INFO" onMatch="ACCEPT" onMismatch="DENY"/>
        </Console>

        <!-- Simple file appender for test logs -->
        <File name="TestFile" fileName="${LOG_PATH}/${APP_NAME}.log">
            <PatternLayout pattern="${CONSOLE_PATTERN}"/>
        </File>
    </Appenders>

    <Loggers>
        <!-- Application specific loggers -->
        <Logger name="com.slack.client" level="DEBUG" additivity="false">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TestFile"/>
        </Logger>

        <!-- Root logger -->
        <Root level="INFO">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="TestFile"/>
        </Root>
    </Loggers>
</Configuration>
