package com.slack.client.service;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for testing scheduler functionality
 * Provides helper methods for date calculations and validation
 */
public class SchedulerTestUtils {

    /**
     * Calculate weekend dates (Friday to Sunday) for a given Monday
     * This mimics the logic used in the weekly scheduler
     */
    public static WeekendPeriod calculateWeekendForMonday(LocalDate monday) {
        if (monday.getDayOfWeek() != DayOfWeek.MONDAY) {
            throw new IllegalArgumentException("Date must be a Monday, got: " + monday.getDayOfWeek());
        }
        
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);
        
        return new WeekendPeriod(friday, sunday);
    }

    /**
     * Generate test scenarios for various Monday dates
     * Useful for comprehensive testing of date calculations
     */
    public static List<TestScenario> generateTestScenarios() {
        List<TestScenario> scenarios = new ArrayList<>();
        
        // Regular Monday in the middle of month
        scenarios.add(new TestScenario(
            "Regular Monday",
            LocalDate.of(2024, 6, 10),
            "Regular Monday in the middle of the month"
        ));
        
        // Monday at the beginning of month (weekend in previous month)
        scenarios.add(new TestScenario(
            "Beginning of Month",
            LocalDate.of(2024, 7, 1),
            "Monday at the beginning of month, weekend in previous month"
        ));
        
        // Monday at the beginning of year
        scenarios.add(new TestScenario(
            "Beginning of Year",
            LocalDate.of(2024, 1, 8),
            "Monday at the beginning of year"
        ));
        
        // Monday in February (leap year consideration)
        scenarios.add(new TestScenario(
            "February Monday",
            LocalDate.of(2024, 2, 5),
            "Monday in February of leap year"
        ));
        
        // Monday at the end of year
        scenarios.add(new TestScenario(
            "End of Year",
            LocalDate.of(2024, 12, 30),
            "Monday at the end of year"
        ));
        
        return scenarios;
    }

    /**
     * Validate that a weekend period is correct
     */
    public static void validateWeekendPeriod(WeekendPeriod weekend) {
        // Validate day of week
        if (weekend.getFriday().getDayOfWeek() != DayOfWeek.FRIDAY) {
            throw new AssertionError("Start date should be Friday, got: " + weekend.getFriday().getDayOfWeek());
        }
        
        if (weekend.getSunday().getDayOfWeek() != DayOfWeek.SUNDAY) {
            throw new AssertionError("End date should be Sunday, got: " + weekend.getSunday().getDayOfWeek());
        }
        
        // Validate order
        if (!weekend.getFriday().isBefore(weekend.getSunday())) {
            throw new AssertionError("Friday should be before Sunday");
        }
        
        // Validate duration (should be exactly 2 days between Friday and Sunday)
        long daysBetween = weekend.getFriday().until(weekend.getSunday()).getDays();
        if (daysBetween != 2) {
            throw new AssertionError("Should be exactly 2 days between Friday and Sunday, got: " + daysBetween);
        }
    }

    /**
     * Format weekend period for display
     */
    public static String formatWeekendPeriod(WeekendPeriod weekend) {
        return String.format("%s to %s",
            weekend.getFriday().format(DateTimeFormatter.ISO_LOCAL_DATE),
            weekend.getSunday().format(DateTimeFormatter.ISO_LOCAL_DATE)
        );
    }

    /**
     * Check if a date is a valid Monday for testing
     */
    public static boolean isValidTestMonday(LocalDate date) {
        return date.getDayOfWeek() == DayOfWeek.MONDAY;
    }

    /**
     * Get the next Monday from a given date
     */
    public static LocalDate getNextMonday(LocalDate date) {
        return date.with(DayOfWeek.MONDAY).plusWeeks(1);
    }

    /**
     * Get the previous Monday from a given date
     */
    public static LocalDate getPreviousMonday(LocalDate date) {
        return date.with(DayOfWeek.MONDAY).minusWeeks(1);
    }

    /**
     * Data class to represent a weekend period
     */
    public static class WeekendPeriod {
        private final LocalDate friday;
        private final LocalDate sunday;

        public WeekendPeriod(LocalDate friday, LocalDate sunday) {
            this.friday = friday;
            this.sunday = sunday;
        }

        public LocalDate getFriday() {
            return friday;
        }

        public LocalDate getSunday() {
            return sunday;
        }

        public String getStartDateString() {
            return friday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        public String getEndDateString() {
            return sunday.format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        @Override
        public String toString() {
            return String.format("WeekendPeriod{friday=%s, sunday=%s}", friday, sunday);
        }
    }

    /**
     * Data class to represent a test scenario
     */
    public static class TestScenario {
        private final String name;
        private final LocalDate monday;
        private final String description;

        public TestScenario(String name, LocalDate monday, String description) {
            this.name = name;
            this.monday = monday;
            this.description = description;
        }

        public String getName() {
            return name;
        }

        public LocalDate getMonday() {
            return monday;
        }

        public String getDescription() {
            return description;
        }

        @Override
        public String toString() {
            return String.format("TestScenario{name='%s', monday=%s, description='%s'}", 
                name, monday, description);
        }
    }
}
