package com.slack.client.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
class SlackSenderSchedulerTest {

    @Mock
    private SlackSenderService slackSenderService;

    @InjectMocks
    private SlackSenderScheduler slackSenderScheduler;

    @BeforeEach
    void setUp() {
        // Reset mocks before each test
        reset(slackSenderService);
    }

    @Test
    void testSendDailyAlertSummaryToSlack_Success() {
        // Given
        doNothing().when(slackSenderService).sendYesterdayAlertSummaryBasedOnMappings();

        // When
        slackSenderScheduler.sendDailyAlertSummaryToSlack();

        // Then
        verify(slackSenderService, times(1)).sendYesterdayAlertSummaryBasedOnMappings();
    }

    @Test
    void testSendDailyAlertSummaryToSlack_Exception() {
        // Given
        doThrow(new RuntimeException("Service error")).when(slackSenderService).sendYesterdayAlertSummaryBasedOnMappings();

        // When & Then - Should not throw exception, should handle it gracefully
        assertDoesNotThrow(() -> slackSenderScheduler.sendDailyAlertSummaryToSlack());
        
        verify(slackSenderService, times(1)).sendYesterdayAlertSummaryBasedOnMappings();
    }

    @Test
    void testSendWeeklyAlertSummaryToSlack_Success() {
        // Given
        doNothing().when(slackSenderService).sendAlertSummaryBasedOnMappings(anyString(), anyString(), anyInt());

        // When
        slackSenderScheduler.sendWeeklyAlertSummaryToSlack();

        // Then
        verify(slackSenderService, times(1)).sendAlertSummaryBasedOnMappings(anyString(), anyString(), eq(50000));
    }

    @Test
    void testSendWeeklyAlertSummaryToSlack_Exception() {
        // Given
        doThrow(new RuntimeException("Service error")).when(slackSenderService)
                .sendAlertSummaryBasedOnMappings(anyString(), anyString(), anyInt());

        // When & Then - Should not throw exception, should handle it gracefully
        assertDoesNotThrow(() -> slackSenderScheduler.sendWeeklyAlertSummaryToSlack());
        
        verify(slackSenderService, times(1)).sendAlertSummaryBasedOnMappings(anyString(), anyString(), eq(50000));
    }

    @Test
    void testWeeklyScheduler_DateCalculation_Monday() {
        // This test verifies the date calculation logic for Monday
        // We'll test the logic separately since we can't easily mock LocalDate.now()
        
        // Given - Simulate it's Monday
        LocalDate monday = LocalDate.of(2024, 1, 8); // A Monday
        
        // Calculate expected dates
        LocalDate expectedFriday = monday.minusDays(4); // Should be Friday (Jan 5, 2024)
        LocalDate expectedSunday = monday.minusDays(1); // Should be Sunday (Jan 7, 2024)
        
        // Then
        assertEquals(DayOfWeek.FRIDAY, expectedFriday.getDayOfWeek());
        assertEquals(DayOfWeek.SUNDAY, expectedSunday.getDayOfWeek());
        assertEquals("2024-01-05", expectedFriday.format(DateTimeFormatter.ISO_LOCAL_DATE));
        assertEquals("2024-01-07", expectedSunday.format(DateTimeFormatter.ISO_LOCAL_DATE));
    }

    @Test
    void testWeeklyScheduler_DateCalculation_DifferentMondays() {
        // Test with different Monday dates to ensure consistency

        // Test Case 1: Monday in the middle of month
        LocalDate monday1 = LocalDate.of(2024, 6, 10); // Monday, June 10, 2024
        LocalDate friday1 = monday1.minusDays(4);
        LocalDate sunday1 = monday1.minusDays(1);

        assertEquals(DayOfWeek.FRIDAY, friday1.getDayOfWeek());
        assertEquals(DayOfWeek.SUNDAY, sunday1.getDayOfWeek());
        assertEquals("2024-06-07", friday1.format(DateTimeFormatter.ISO_LOCAL_DATE));
        assertEquals("2024-06-09", sunday1.format(DateTimeFormatter.ISO_LOCAL_DATE));

        // Test Case 2: Monday at the beginning of month (weekend in previous month)
        LocalDate monday2 = LocalDate.of(2024, 7, 1); // Monday, July 1, 2024
        LocalDate friday2 = monday2.minusDays(4);
        LocalDate sunday2 = monday2.minusDays(1);

        assertEquals(DayOfWeek.FRIDAY, friday2.getDayOfWeek());
        assertEquals(DayOfWeek.SUNDAY, sunday2.getDayOfWeek());
        assertEquals("2024-06-28", friday2.format(DateTimeFormatter.ISO_LOCAL_DATE)); // Previous month
        assertEquals("2024-06-30", sunday2.format(DateTimeFormatter.ISO_LOCAL_DATE)); // Previous month
    }

    @Test
    void testWeeklyScheduler_DateCalculation_YearBoundary() {
        // Test when Monday is in January and weekend is in previous year
        LocalDate monday = LocalDate.of(2024, 1, 8); // Monday, January 8, 2024 (actual Monday)
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);

        assertEquals(DayOfWeek.FRIDAY, friday.getDayOfWeek());
        assertEquals(DayOfWeek.SUNDAY, sunday.getDayOfWeek());
        assertEquals("2024-01-05", friday.format(DateTimeFormatter.ISO_LOCAL_DATE)); // Same year
        assertEquals("2024-01-07", sunday.format(DateTimeFormatter.ISO_LOCAL_DATE)); // Same year
    }

    @Test
    void testWeeklyScheduler_ParameterValidation() {
        // Given
        doNothing().when(slackSenderService).sendAlertSummaryBasedOnMappings(anyString(), anyString(), anyInt());

        // When
        slackSenderScheduler.sendWeeklyAlertSummaryToSlack();

        // Then - Verify that the method is called with correct limit
        verify(slackSenderService).sendAlertSummaryBasedOnMappings(
                argThat(startDate -> startDate.matches("\\d{4}-\\d{2}-\\d{2}")), // ISO date format
                argThat(endDate -> endDate.matches("\\d{4}-\\d{2}-\\d{2}")),     // ISO date format
                eq(50000) // High limit for weekend data
        );
    }

    @Test
    void testWeeklyScheduler_DateOrder() {
        // This test ensures that startDate (Friday) is before endDate (Sunday)
        LocalDate monday = LocalDate.now().with(DayOfWeek.MONDAY);
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);
        
        // Then
        assertTrue(friday.isBefore(sunday), "Friday should be before Sunday");
        assertEquals(2, friday.until(sunday).getDays(), "There should be 2 days between Friday and Sunday");
    }
}
