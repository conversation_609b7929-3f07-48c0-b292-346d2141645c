package com.slack.client.service;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.util.List;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Comprehensive test for weekend date calculation logic used in the weekly scheduler
 */
class WeekendDateCalculationTest {

    @Test
    @DisplayName("Test weekend calculation for a regular Monday")
    void testRegularMondayWeekendCalculation() {
        // Given
        LocalDate monday = LocalDate.of(2024, 6, 10); // Monday, June 10, 2024
        
        // When
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
        
        // Then
        assertEquals(LocalDate.of(2024, 6, 7), weekend.getFriday()); // Friday, June 7, 2024
        assertEquals(LocalDate.of(2024, 6, 9), weekend.getSunday()); // Sunday, June 9, 2024
        
        // Validate the weekend period
        SchedulerTestUtils.validateWeekendPeriod(weekend);
    }

    @Test
    @DisplayName("Test weekend calculation when Monday is at beginning of month")
    void testBeginningOfMonthMondayWeekendCalculation() {
        // Given
        LocalDate monday = LocalDate.of(2024, 7, 1); // Monday, July 1, 2024
        
        // When
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
        
        // Then
        assertEquals(LocalDate.of(2024, 6, 28), weekend.getFriday()); // Friday, June 28, 2024 (previous month)
        assertEquals(LocalDate.of(2024, 6, 30), weekend.getSunday()); // Sunday, June 30, 2024 (previous month)
        
        // Validate the weekend period
        SchedulerTestUtils.validateWeekendPeriod(weekend);
    }

    @Test
    @DisplayName("Test weekend calculation when Monday is at beginning of year")
    void testBeginningOfYearMondayWeekendCalculation() {
        // Given
        LocalDate monday = LocalDate.of(2024, 1, 8); // Monday, January 8, 2024 (actual Monday)

        // When
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);

        // Then
        assertEquals(LocalDate.of(2024, 1, 5), weekend.getFriday()); // Friday, January 5, 2024
        assertEquals(LocalDate.of(2024, 1, 7), weekend.getSunday()); // Sunday, January 7, 2024

        // Validate the weekend period
        SchedulerTestUtils.validateWeekendPeriod(weekend);
    }

    @Test
    @DisplayName("Test that invalid input throws exception")
    void testInvalidInputThrowsException() {
        // Given
        LocalDate tuesday = LocalDate.of(2024, 6, 11); // Tuesday, not Monday
        
        // When & Then
        assertThrows(IllegalArgumentException.class, () -> {
            SchedulerTestUtils.calculateWeekendForMonday(tuesday);
        });
    }

    @ParameterizedTest
    @MethodSource("provideTestScenarios")
    @DisplayName("Test weekend calculation for various Monday scenarios")
    void testVariousMondayScenarios(SchedulerTestUtils.TestScenario scenario) {
        // Given
        LocalDate monday = scenario.getMonday();
        
        // When
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
        
        // Then
        assertNotNull(weekend);
        SchedulerTestUtils.validateWeekendPeriod(weekend);
        
        // Additional validations
        assertEquals(DayOfWeek.FRIDAY, weekend.getFriday().getDayOfWeek());
        assertEquals(DayOfWeek.SUNDAY, weekend.getSunday().getDayOfWeek());
        assertTrue(weekend.getFriday().isBefore(weekend.getSunday()));
        
        // Log the result for manual verification
        System.out.printf("Scenario: %s - Monday %s -> Weekend: %s%n", 
            scenario.getName(), 
            monday, 
            SchedulerTestUtils.formatWeekendPeriod(weekend));
    }

    /**
     * Provide test scenarios for parameterized test
     */
    static Stream<SchedulerTestUtils.TestScenario> provideTestScenarios() {
        return SchedulerTestUtils.generateTestScenarios().stream();
    }

    @Test
    @DisplayName("Test weekend period formatting")
    void testWeekendPeriodFormatting() {
        // Given
        LocalDate monday = LocalDate.of(2024, 6, 10);
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
        
        // When
        String formatted = SchedulerTestUtils.formatWeekendPeriod(weekend);
        String startDate = weekend.getStartDateString();
        String endDate = weekend.getEndDateString();
        
        // Then
        assertEquals("2024-06-07 to 2024-06-09", formatted);
        assertEquals("2024-06-07", startDate);
        assertEquals("2024-06-09", endDate);
    }

    @Test
    @DisplayName("Test utility methods for Monday validation")
    void testMondayValidationUtilities() {
        // Test valid Monday
        LocalDate monday = LocalDate.of(2024, 6, 10);
        assertTrue(SchedulerTestUtils.isValidTestMonday(monday));
        
        // Test invalid day (Tuesday)
        LocalDate tuesday = LocalDate.of(2024, 6, 11);
        assertFalse(SchedulerTestUtils.isValidTestMonday(tuesday));
        
        // Test next Monday calculation
        LocalDate nextMonday = SchedulerTestUtils.getNextMonday(monday);
        assertEquals(LocalDate.of(2024, 6, 17), nextMonday);
        assertTrue(SchedulerTestUtils.isValidTestMonday(nextMonday));
        
        // Test previous Monday calculation
        LocalDate previousMonday = SchedulerTestUtils.getPreviousMonday(monday);
        assertEquals(LocalDate.of(2024, 6, 3), previousMonday);
        assertTrue(SchedulerTestUtils.isValidTestMonday(previousMonday));
    }

    @Test
    @DisplayName("Test leap year scenario")
    void testLeapYearScenario() {
        // Given - Monday in a leap year February
        LocalDate monday = LocalDate.of(2024, 3, 4); // Monday, March 4, 2024 (2024 is a leap year)
        
        // When
        SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
        
        // Then
        assertEquals(LocalDate.of(2024, 3, 1), weekend.getFriday()); // Friday, March 1, 2024
        assertEquals(LocalDate.of(2024, 3, 3), weekend.getSunday()); // Sunday, March 3, 2024

        // Validate the weekend period
        SchedulerTestUtils.validateWeekendPeriod(weekend);

        // Verify the dates are correct
        assertEquals(1, weekend.getFriday().getDayOfMonth());
        assertEquals(3, weekend.getFriday().getMonthValue());
    }

    @Test
    @DisplayName("Test weekend period duration consistency")
    void testWeekendPeriodDurationConsistency() {
        // Test multiple Mondays to ensure consistent 3-day weekend period
        LocalDate[] testMondays = {
            LocalDate.of(2024, 1, 8),
            LocalDate.of(2024, 4, 15),
            LocalDate.of(2024, 7, 22),
            LocalDate.of(2024, 10, 28),
            LocalDate.of(2024, 12, 30)
        };
        
        for (LocalDate monday : testMondays) {
            SchedulerTestUtils.WeekendPeriod weekend = SchedulerTestUtils.calculateWeekendForMonday(monday);
            
            // Weekend should always span exactly 3 days (Friday, Saturday, Sunday)
            long totalDays = weekend.getFriday().until(weekend.getSunday().plusDays(1)).getDays();
            assertEquals(3, totalDays, "Weekend should always span 3 days for Monday: " + monday);
            
            // Days between Friday and Sunday should always be 2
            long daysBetween = weekend.getFriday().until(weekend.getSunday()).getDays();
            assertEquals(2, daysBetween, "Should be 2 days between Friday and Sunday for Monday: " + monday);
        }
    }
}
