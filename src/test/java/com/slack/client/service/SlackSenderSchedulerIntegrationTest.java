package com.slack.client.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDate;
import java.time.DayOfWeek;
import java.time.format.DateTimeFormatter;

import static org.mockito.Mockito.*;

/**
 * Integration test for SlackSenderScheduler
 * Tests the scheduler in a Spring context with mocked dependencies
 */
@SpringBootTest
@TestPropertySource(properties = {
    "logging.level.com.slack.client=DEBUG"
})
class SlackSenderSchedulerIntegrationTest {

    @MockBean
    private SlackSenderService slackSenderService;

    @Test
    void contextLoads() {
        // This test ensures that the Spring context loads successfully
        // with the scheduler bean properly configured
    }

    @Test
    void testSchedulerMethodsExist() {
        // Verify that the scheduler methods can be called without Spring context issues
        SlackSenderScheduler scheduler = new SlackSenderScheduler();
        
        // This would normally fail due to null dependencies, but we're just testing method existence
        // In a real scenario, these would be called by Spring's scheduler
        
        // The actual scheduling is tested through cron expressions in production
        // We can't easily test cron scheduling in unit tests without complex setup
    }

    /**
     * Test helper method to validate weekend date calculation logic
     * This simulates what happens when the weekly scheduler runs on different Mondays
     */
    @Test
    void testWeekendDateCalculationForVariousMondays() {
        // Test multiple Monday scenarios
        LocalDate[] testMondays = {
            LocalDate.of(2024, 1, 8),   // January 8, 2024 (Monday)
            LocalDate.of(2024, 2, 5),   // February 5, 2024 (Monday)
            LocalDate.of(2024, 3, 4),   // March 4, 2024 (Monday)
            LocalDate.of(2024, 6, 3),   // June 3, 2024 (Monday)
            LocalDate.of(2024, 12, 30), // December 30, 2024 (Monday)
            LocalDate.of(2025, 1, 6)    // January 6, 2025 (Monday)
        };

        for (LocalDate monday : testMondays) {
            // Calculate weekend dates as the scheduler would
            LocalDate friday = monday.minusDays(4);
            LocalDate sunday = monday.minusDays(1);
            
            // Validate the calculation
            assert friday.getDayOfWeek() == DayOfWeek.FRIDAY : 
                "Expected Friday, got " + friday.getDayOfWeek() + " for Monday " + monday;
            assert sunday.getDayOfWeek() == DayOfWeek.SUNDAY : 
                "Expected Sunday, got " + sunday.getDayOfWeek() + " for Monday " + monday;
            assert friday.isBefore(sunday) : 
                "Friday should be before Sunday for Monday " + monday;
            
            System.out.println(String.format(
                "Monday %s -> Weekend: %s to %s", 
                monday.format(DateTimeFormatter.ISO_LOCAL_DATE),
                friday.format(DateTimeFormatter.ISO_LOCAL_DATE),
                sunday.format(DateTimeFormatter.ISO_LOCAL_DATE)
            ));
        }
    }

    /**
     * Test the cron expression validation
     * This ensures our cron expressions are valid
     */
    @Test
    void testCronExpressions() {
        // Daily scheduler: "0 0 10 * * ?" - Every day at 10:00 AM
        String dailyCron = "0 0 10 * * ?";
        
        // Weekly scheduler: "0 45 9 * * MON" - Every Monday at 9:45 AM
        String weeklyCron = "0 45 9 * * MON";
        
        // These are valid cron expressions
        // In a real test, we could use a cron parser library to validate them
        // For now, we just ensure they follow the expected pattern
        
        assert dailyCron.matches("\\d+ \\d+ \\d+ \\* \\* \\?") : "Daily cron expression format is invalid";
        assert weeklyCron.matches("\\d+ \\d+ \\d+ \\* \\* \\w+") : "Weekly cron expression format is invalid";
        
        System.out.println("Daily scheduler cron: " + dailyCron);
        System.out.println("Weekly scheduler cron: " + weeklyCron);
    }

    /**
     * Test that verifies the weekend period covers exactly 3 days
     */
    @Test
    void testWeekendPeriodDuration() {
        LocalDate monday = LocalDate.of(2024, 6, 10); // Any Monday
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);
        
        // Weekend should cover Friday, Saturday, and Sunday (3 days)
        long daysBetween = friday.until(sunday.plusDays(1)).getDays(); // +1 to include Sunday
        
        assert daysBetween == 3 : "Weekend period should cover exactly 3 days (Fri, Sat, Sun), got " + daysBetween;
        
        System.out.println(String.format(
            "Weekend period: %s to %s (%d days)",
            friday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            sunday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            daysBetween
        ));
    }

    /**
     * Test edge case: Monday at the beginning of the year
     */
    @Test
    void testNewYearMondayScenario() {
        // Test when Monday is early in January

        LocalDate monday = LocalDate.of(2024, 1, 8); // January 8, 2024 is a Monday
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);

        // Verify dates
        assert friday.getYear() == 2024 : "Friday should be in same year";
        assert sunday.getYear() == 2024 : "Sunday should be in same year";
        assert friday.getMonthValue() == 1 : "Friday should be in January";
        assert sunday.getMonthValue() == 1 : "Sunday should be in January";

        System.out.println(String.format(
            "New Year scenario - Monday %s -> Weekend: %s to %s",
            monday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            friday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            sunday.format(DateTimeFormatter.ISO_LOCAL_DATE)
        ));
    }

    /**
     * Test edge case: Monday at the beginning of the month
     */
    @Test
    void testBeginningOfMonthScenario() {
        // Test when Monday is the 1st of the month
        // The weekend would be in the previous month
        
        LocalDate monday = LocalDate.of(2024, 7, 1); // July 1, 2024 was a Monday
        LocalDate friday = monday.minusDays(4);
        LocalDate sunday = monday.minusDays(1);
        
        // Verify dates
        assert friday.getMonthValue() == 6 : "Friday should be in previous month (June)";
        assert sunday.getMonthValue() == 6 : "Sunday should be in previous month (June)";
        
        System.out.println(String.format(
            "Beginning of month scenario - Monday %s -> Weekend: %s to %s",
            monday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            friday.format(DateTimeFormatter.ISO_LOCAL_DATE),
            sunday.format(DateTimeFormatter.ISO_LOCAL_DATE)
        ));
    }
}
